{"version": 3, "sources": ["wpr-beacon.js"], "sourcesContent": ["(() => {\n  // src/Utils.js\n  var BeaconUtils = class {\n    static getScreenWidth() {\n      return window.innerWidth || document.documentElement.clientWidth;\n    }\n    static getScreenHeight() {\n      return window.innerHeight || document.documentElement.clientHeight;\n    }\n    static isNotValidScreensize(is_mobile, threshold) {\n      const screenWidth = this.getScreenWidth();\n      const screenHeight = this.getScreenHeight();\n      const isNotValidForMobile = is_mobile && (screenWidth > threshold.width || screenHeight > threshold.height);\n      const isNotValidForDesktop = !is_mobile && (screenWidth < threshold.width || screenHeight < threshold.height);\n      return isNotValidForMobile || isNotValidForDesktop;\n    }\n    static isPageCached() {\n      const signature = document.documentElement.nextSibling && document.documentElement.nextSibling.data ? document.documentElement.nextSibling.data : \"\";\n      return signature && signature.includes(\"Debug: cached\");\n    }\n    static isIntersecting(rect) {\n      return rect.bottom >= 0 && rect.right >= 0 && rect.top <= (window.innerHeight || document.documentElement.clientHeight) && rect.left <= (window.innerWidth || document.documentElement.clientWidth);\n    }\n    static isPageScrolled() {\n      return window.pageYOffset > 0 || document.documentElement.scrollTop > 0;\n    }\n    /**\n     * Checks if an element is visible in the viewport.\n     * \n     * This method checks if the provided element is visible in the viewport by\n     * considering its display, visibility, opacity, width, and height properties.\n     * It also excludes elements with transparent text properties.\n     * It returns true if the element is visible, and false otherwise.\n     * \n     * @param {Element} element - The element to check for visibility.\n     * @returns {boolean} True if the element is visible, false otherwise.\n     */\n    static isElementVisible(element) {\n      const style = window.getComputedStyle(element);\n      const rect = element.getBoundingClientRect();\n      if (!style) {\n        return false;\n      }\n      if (this.hasTransparentText(element)) {\n        return false;\n      }\n      return !(style.display === \"none\" || style.visibility === \"hidden\" || style.opacity === \"0\" || rect.width === 0 || rect.height === 0);\n    }\n    /**\n     * Checks if an element has transparent text properties.\n     *\n     * This method checks for specific CSS properties that make text invisible,\n     * such as `color: transparent`, `color: rgba(..., 0)`, `color: hsla(..., 0)`,\n     * `color: #...00` (8-digit hex with alpha = 0), and `filter: opacity(0)`.\n     *\n     * @param {Element} element - The element to check.\n     * @returns {boolean} True if the element has transparent text properties, false otherwise.\n     */\n    static hasTransparentText(element) {\n      const style = window.getComputedStyle(element);\n      if (!style) {\n        return false;\n      }\n      const color = style.color || \"\";\n      const filter = style.filter || \"\";\n      if (color === \"transparent\") {\n        return true;\n      }\n      const rgbaMatch = color.match(/rgba\\(\\d+,\\s*\\d+,\\s*\\d+,\\s*0\\)/);\n      if (rgbaMatch) {\n        return true;\n      }\n      const hslaMatch = color.match(/hsla\\(\\d+,\\s*\\d+%,\\s*\\d+%,\\s*0\\)/);\n      if (hslaMatch) {\n        return true;\n      }\n      const hexMatch = color.match(/#[0-9a-fA-F]{6}00/);\n      if (hexMatch) {\n        return true;\n      }\n      if (filter.includes(\"opacity(0)\")) {\n        return true;\n      }\n      return false;\n    }\n  };\n  var Utils_default = BeaconUtils;\n\n  // src/BeaconLcp.js\n  var BeaconLcp = class {\n    constructor(config, logger) {\n      this.config = config;\n      this.performanceImages = [];\n      this.logger = logger;\n    }\n    async run() {\n      try {\n        const above_the_fold_images = this._generateLcpCandidates(Infinity);\n        if (above_the_fold_images) {\n          this._initWithFirstElementWithInfo(above_the_fold_images);\n          this._fillATFWithoutDuplications(above_the_fold_images);\n        }\n      } catch (err) {\n        this.errorCode = \"script_error\";\n        this.logger.logMessage(\"Script Error: \" + err);\n      }\n    }\n    _generateLcpCandidates(count) {\n      const lcpElements = document.querySelectorAll(this.config.elements);\n      if (lcpElements.length <= 0) {\n        return [];\n      }\n      const potentialCandidates = Array.from(lcpElements);\n      const topCandidates = potentialCandidates.map((element) => {\n        if (\"img\" === element.nodeName.toLowerCase() && \"picture\" === element.parentElement.nodeName.toLowerCase()) {\n          return null;\n        }\n        let rect;\n        if (\"picture\" === element.nodeName.toLowerCase()) {\n          const imgElement = element.querySelector(\"img\");\n          if (imgElement) {\n            rect = imgElement.getBoundingClientRect();\n          } else {\n            return null;\n          }\n        } else {\n          rect = element.getBoundingClientRect();\n        }\n        return {\n          element,\n          rect\n        };\n      }).filter((item) => item !== null).filter((item) => {\n        return item.rect.width > 0 && item.rect.height > 0 && Utils_default.isIntersecting(item.rect) && Utils_default.isElementVisible(item.element);\n      }).map((item) => ({\n        item,\n        area: this._getElementArea(item.rect),\n        elementInfo: this._getElementInfo(item.element)\n      })).sort((a, b) => b.area - a.area).slice(0, count);\n      return topCandidates.map((candidate) => ({\n        element: candidate.item.element,\n        elementInfo: candidate.elementInfo\n      }));\n    }\n    _getElementArea(rect) {\n      const visibleWidth = Math.min(rect.width, (window.innerWidth || document.documentElement.clientWidth) - rect.left);\n      const visibleHeight = Math.min(rect.height, (window.innerHeight || document.documentElement.clientHeight) - rect.top);\n      return visibleWidth * visibleHeight;\n    }\n    _getElementInfo(element) {\n      const nodeName = element.nodeName.toLowerCase();\n      const element_info = {\n        type: \"\",\n        src: \"\",\n        srcset: \"\",\n        sizes: \"\",\n        sources: [],\n        bg_set: [],\n        current_src: \"\"\n      };\n      const css_bg_url_rgx = /url\\(\\s*?['\"]?\\s*?(.+?)\\s*?[\"']?\\s*?\\)/ig;\n      if (nodeName === \"img\" && element.srcset) {\n        element_info.type = \"img-srcset\";\n        element_info.src = element.src;\n        element_info.srcset = element.srcset;\n        element_info.sizes = element.sizes;\n        element_info.current_src = element.currentSrc;\n      } else if (nodeName === \"img\") {\n        element_info.type = \"img\";\n        element_info.src = element.src;\n        element_info.current_src = element.currentSrc;\n      } else if (nodeName === \"video\") {\n        element_info.type = \"img\";\n        const source = element.querySelector(\"source\");\n        element_info.src = element.poster || (source ? source.src : \"\");\n        element_info.current_src = element_info.src;\n      } else if (nodeName === \"svg\") {\n        const imageElement = element.querySelector(\"image\");\n        if (imageElement) {\n          element_info.type = \"img\";\n          element_info.src = imageElement.getAttribute(\"href\") || \"\";\n          element_info.current_src = element_info.src;\n        }\n      } else if (nodeName === \"picture\") {\n        element_info.type = \"picture\";\n        const img = element.querySelector(\"img\");\n        element_info.src = img ? img.src : \"\";\n        element_info.sources = Array.from(element.querySelectorAll(\"source\")).map((source) => ({\n          srcset: source.srcset || \"\",\n          media: source.media || \"\",\n          type: source.type || \"\",\n          sizes: source.sizes || \"\"\n        }));\n      } else {\n        const computed_style = window.getComputedStyle(element, null);\n        const bg_props = [\n          computed_style.getPropertyValue(\"background-image\"),\n          getComputedStyle(element, \":after\").getPropertyValue(\"background-image\"),\n          getComputedStyle(element, \":before\").getPropertyValue(\"background-image\")\n        ].filter((prop) => prop !== \"none\");\n        if (bg_props.length === 0) {\n          return null;\n        }\n        const full_bg_prop = bg_props[0];\n        element_info.type = \"bg-img\";\n        if (full_bg_prop.includes(\"image-set(\")) {\n          element_info.type = \"bg-img-set\";\n        }\n        if (!full_bg_prop || full_bg_prop === \"\" || full_bg_prop.includes(\"data:image\")) {\n          return null;\n        }\n        const matches = [...full_bg_prop.matchAll(css_bg_url_rgx)];\n        element_info.bg_set = matches.map((m) => m[1] ? { src: m[1].trim() + (m[2] ? \" \" + m[2].trim() : \"\") } : {});\n        if (element_info.bg_set.every((item) => item.src === \"\")) {\n          element_info.bg_set = matches.map((m) => m[1] ? { src: m[1].trim() } : {});\n        }\n        if (element_info.bg_set.length <= 0) {\n          return null;\n        }\n        if (element_info.bg_set.length > 0) {\n          element_info.src = element_info.bg_set[0].src;\n          if (element_info.type === \"bg-img-set\") {\n            element_info.src = element_info.bg_set;\n          }\n        }\n      }\n      return element_info;\n    }\n    _initWithFirstElementWithInfo(elements) {\n      const firstElementWithInfo = elements.find((item) => {\n        return item.elementInfo !== null && (item.elementInfo.src || item.elementInfo.srcset);\n      });\n      if (!firstElementWithInfo) {\n        this.logger.logMessage(\"No LCP candidate found.\");\n        this.performanceImages = [];\n        return;\n      }\n      this.performanceImages = [{\n        ...firstElementWithInfo.elementInfo,\n        label: \"lcp\"\n      }];\n    }\n    _fillATFWithoutDuplications(elements) {\n      elements.forEach(({ element, elementInfo }) => {\n        if (this._isDuplicateImage(element) || !elementInfo) {\n          return;\n        }\n        this.performanceImages.push({ ...elementInfo, label: \"above-the-fold\" });\n      });\n    }\n    _isDuplicateImage(image) {\n      const elementInfo = this._getElementInfo(image);\n      if (elementInfo === null) {\n        return false;\n      }\n      const isImageOrVideo = elementInfo.type === \"img\" || elementInfo.type === \"img-srcset\" || elementInfo.type === \"video\";\n      const isBgImageOrPicture = elementInfo.type === \"bg-img\" || elementInfo.type === \"bg-img-set\" || elementInfo.type === \"picture\";\n      return (isImageOrVideo || isBgImageOrPicture) && this.performanceImages.some((item) => item.src === elementInfo.src);\n    }\n    getResults() {\n      return this.performanceImages;\n    }\n  };\n  var BeaconLcp_default = BeaconLcp;\n\n  // src/BeaconLrc.js\n  var BeaconLrc = class {\n    constructor(config, logger) {\n      this.config = config;\n      this.logger = logger;\n      this.lazyRenderElements = [];\n    }\n    async run() {\n      try {\n        const elementsInView = this._getLazyRenderElements();\n        if (elementsInView) {\n          this._processElements(elementsInView);\n        }\n      } catch (err) {\n        this.errorCode = \"script_error\";\n        this.logger.logMessage(\"Script Error: \" + err);\n      }\n    }\n    _getLazyRenderElements() {\n      const elements = document.querySelectorAll(\"[data-rocket-location-hash]\");\n      const svgUseTargets = this._getSvgUseTargets();\n      if (elements.length <= 0) {\n        return [];\n      }\n      const validElements = Array.from(elements).filter((element) => {\n        if (this._skipElement(element)) {\n          return false;\n        }\n        if (svgUseTargets.includes(element)) {\n          this.logger.logColoredMessage(`Element skipped because of SVG: ${element.tagName}`, \"orange\");\n          return false;\n        }\n        return true;\n      });\n      return validElements.map((element) => ({\n        element,\n        depth: this._getElementDepth(element),\n        distance: this._getElementDistance(element),\n        hash: this._getLocationHash(element)\n      }));\n    }\n    _getElementDepth(element) {\n      let depth = 0;\n      let parent = element.parentElement;\n      while (parent) {\n        depth++;\n        parent = parent.parentElement;\n      }\n      return depth;\n    }\n    _getElementDistance(element) {\n      const rect = element.getBoundingClientRect();\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      return Math.max(0, rect.top + scrollTop - Utils_default.getScreenHeight());\n    }\n    _skipElement(element) {\n      const skipStrings = this.config.skipStrings || [\"memex\"];\n      if (!element || !element.id) return false;\n      return skipStrings.some((str) => element.id.toLowerCase().includes(str.toLowerCase()));\n    }\n    _shouldSkipElement(element, exclusions) {\n      if (!element) return false;\n      for (let i = 0; i < exclusions.length; i++) {\n        const [attribute, pattern] = exclusions[i];\n        const attributeValue = element.getAttribute(attribute);\n        if (attributeValue && new RegExp(pattern, \"i\").test(attributeValue)) {\n          return true;\n        }\n      }\n      return false;\n    }\n    _checkLcrConflict(element) {\n      const conflictingElements = [];\n      const computedStyle = window.getComputedStyle(element);\n      const validMargins = [\"marginTop\", \"marginRight\", \"marginBottom\", \"marginLeft\"];\n      const negativeMargins = validMargins.some((margin) => parseFloat(computedStyle[margin]) < 0);\n      const currentElementConflicts = negativeMargins || computedStyle.contentVisibility === \"auto\" || computedStyle.contentVisibility === \"hidden\";\n      if (currentElementConflicts) {\n        conflictingElements.push({\n          element,\n          conflicts: [\n            negativeMargins && \"negative margin\",\n            computedStyle.contentVisibility === \"auto\" && \"content-visibility:auto\",\n            computedStyle.contentVisibility === \"hidden\" && \"content-visibility:hidden\"\n          ].filter(Boolean)\n        });\n      }\n      Array.from(element.children).forEach((child) => {\n        const childStyle = window.getComputedStyle(child);\n        const validMargins2 = [\"marginTop\", \"marginRight\", \"marginBottom\", \"marginLeft\"];\n        const childNegativeMargins = validMargins2.some((margin) => parseFloat(childStyle[margin]) < 0);\n        const childConflicts = childNegativeMargins || childStyle.position === \"absolute\" || childStyle.position === \"fixed\";\n        if (childConflicts) {\n          conflictingElements.push({\n            element: child,\n            conflicts: [\n              childNegativeMargins && \"negative margin\",\n              childStyle.position === \"absolute\" && \"position:absolute\",\n              childStyle.position === \"fixed\" && \"position:fixed\"\n            ].filter(Boolean)\n          });\n        }\n      });\n      return conflictingElements;\n    }\n    _processElements(elements) {\n      elements.forEach(({ element, depth, distance, hash }) => {\n        if (this._shouldSkipElement(element, this.config.exclusions || [])) {\n          return;\n        }\n        if (\"No hash detected\" === hash) {\n          return;\n        }\n        const conflicts = this._checkLcrConflict(element);\n        if (conflicts.length > 0) {\n          this.logger.logMessage(\"Skipping element due to conflicts:\", conflicts);\n          return;\n        }\n        const can_push_hash = element.parentElement && this._getElementDistance(element.parentElement) < this.config.lrc_threshold && distance >= this.config.lrc_threshold;\n        const color = can_push_hash ? \"green\" : distance === 0 ? \"red\" : \"\";\n        this.logger.logColoredMessage(`${\"\t\".repeat(depth)}${element.tagName} (Depth: ${depth}, Distance from viewport bottom: ${distance}px)`, color);\n        this.logger.logColoredMessage(`${\"\t\".repeat(depth)}Location hash: ${hash}`, color);\n        this.logger.logColoredMessage(`${\"\t\".repeat(depth)}Dimensions Client Height: ${element.clientHeight}`, color);\n        if (can_push_hash) {\n          this.lazyRenderElements.push(hash);\n          this.logger.logMessage(`Element pushed with hash: ${hash}`);\n        }\n      });\n    }\n    _getXPath(element) {\n      if (element && element.id !== \"\") {\n        return `//*[@id=\"${element.id}\"]`;\n      }\n      return this._getElementXPath(element);\n    }\n    _getElementXPath(element) {\n      if (element === document.body) {\n        return \"/html/body\";\n      }\n      const position = this._getElementPosition(element);\n      return `${this._getElementXPath(element.parentNode)}/${element.nodeName.toLowerCase()}[${position}]`;\n    }\n    _getElementPosition(element) {\n      let pos = 1;\n      let sibling = element.previousElementSibling;\n      while (sibling) {\n        if (sibling.nodeName === element.nodeName) {\n          pos++;\n        }\n        sibling = sibling.previousElementSibling;\n      }\n      return pos;\n    }\n    _getLocationHash(element) {\n      return element.hasAttribute(\"data-rocket-location-hash\") ? element.getAttribute(\"data-rocket-location-hash\") : \"No hash detected\";\n    }\n    _getSvgUseTargets() {\n      const useElements = document.querySelectorAll(\"use\");\n      const targets = /* @__PURE__ */ new Set();\n      useElements.forEach((use) => {\n        let parent = use.parentElement;\n        while (parent && parent !== document.body) {\n          targets.add(parent);\n          parent = parent.parentElement;\n        }\n      });\n      return Array.from(targets);\n    }\n    getResults() {\n      return this.lazyRenderElements;\n    }\n  };\n  var BeaconLrc_default = BeaconLrc;\n\n  // src/BeaconPreloadFonts.js\n  var BeaconPreloadFonts = class {\n    constructor(config, logger) {\n      this.config = config;\n      this.logger = logger;\n      this.aboveTheFoldFonts = [];\n      const extensions = (Array.isArray(this.config.processed_extensions) && this.config.processed_extensions.length > 0 ? this.config.processed_extensions : [\"woff\", \"woff2\", \"ttf\"]).map((ext) => ext.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\")).join(\"|\");\n      this.FONT_FILE_REGEX = new RegExp(`\\\\.(${extensions})(\\\\?.*)?$`, \"i\");\n      this.EXCLUDED_TAG_NAMES = /* @__PURE__ */ new Set([\n        // Metadata/document head\n        \"BASE\",\n        \"HEAD\",\n        \"LINK\",\n        \"META\",\n        \"STYLE\",\n        \"TITLE\",\n        \"SCRIPT\",\n        // Media\n        \"IMG\",\n        \"VIDEO\",\n        \"AUDIO\",\n        \"EMBED\",\n        \"OBJECT\",\n        \"IFRAME\",\n        // Templating, wrappers, components, fallback\n        \"NOSCRIPT\",\n        \"TEMPLATE\",\n        \"SLOT\",\n        \"CANVAS\",\n        // Resources\n        \"SOURCE\",\n        \"TRACK\",\n        \"PARAM\",\n        // SVG references\n        \"USE\",\n        \"SYMBOL\",\n        // Layout work\n        \"BR\",\n        \"HR\",\n        \"WBR\",\n        // Obsolete/deprecated\n        \"APPLET\",\n        \"ACRONYM\",\n        \"BGSOUND\",\n        \"BIG\",\n        \"BLINK\",\n        \"CENTER\",\n        \"FONT\",\n        \"FRAME\",\n        \"FRAMESET\",\n        \"MARQUEE\",\n        \"NOFRAMES\",\n        \"STRIKE\",\n        \"TT\",\n        \"U\",\n        \"XMP\"\n      ]);\n    }\n    /**\n     * Checks if a URL should be excluded from external font processing based on domain exclusions.\n     * \n     * @param {string} url - The URL to check.\n     * @returns {boolean} True if the URL should be excluded, false otherwise.\n     */\n    isUrlExcludedFromExternalProcessing(url) {\n      if (!url) return false;\n      const externalFontExclusions = this.config.external_font_exclusions || [];\n      const preloadFontsExclusions = this.config.preload_fonts_exclusions || [];\n      const allExclusions = [...externalFontExclusions, ...preloadFontsExclusions];\n      return allExclusions.some((exclusion) => url.includes(exclusion));\n    }\n    /**\n     * Checks if a font family or URL should be excluded from preloading.\n     * \n     * This method determines if the provided font family or any of its URLs\n     * match any exclusion patterns defined in the configuration. It checks for\n     * exact matches and substring matches for both the font family and URLs.\n     * \n     * @param {string} fontFamily - The font family to check.\n     * @param {string[]} urls - Array of font file URLs to check.\n     * @returns {boolean} True if the font should be excluded, false otherwise.\n     */\n    isExcluded(fontFamily, urls) {\n      const exclusions = this.config.preload_fonts_exclusions;\n      const exclusionsSet = new Set(exclusions);\n      if (exclusionsSet.has(fontFamily)) {\n        return true;\n      }\n      if (exclusions.some((exclusion) => fontFamily.includes(exclusion))) {\n        return true;\n      }\n      if (Array.isArray(urls) && urls.length > 0) {\n        if (urls.some((url) => exclusionsSet.has(url))) {\n          return true;\n        }\n        if (urls.some(\n          (url) => exclusions.some((exclusion) => url.includes(exclusion))\n        )) {\n          return true;\n        }\n      }\n      return false;\n    }\n    /**\n     * Checks if an element can be styled with font-family.\n     * \n     * This method determines if the provided element's tag name is not in the list\n     * of excluded tag names that cannot be styled with font-family CSS property.\n     * \n     * @param {Element} element - The element to check.\n     * @returns {boolean} True if the element can be styled with font-family, false otherwise.\n     */\n    canElementBeStyledWithFontFamily(element) {\n      return !this.EXCLUDED_TAG_NAMES.has(element.tagName);\n    }\n    /**\n     * Checks if an element is visible in the viewport.\n     * \n     * This method delegates to BeaconUtils.isElementVisible() for consistent\n     * visibility checking across all beacons.\n     * \n     * @param {Element} element - The element to check for visibility.\n     * @returns {boolean} True if the element is visible, false otherwise.\n     */\n    isElementVisible(element) {\n      return Utils_default.isElementVisible(element);\n    }\n    /**\n     * Cleans a URL by removing query parameters and fragments.\n     * \n     * This method takes a URL as input, removes any query parameters and fragments,\n     * and returns the cleaned URL.\n     * \n     * @param {string} url - The URL to clean.\n     * @returns {string} The cleaned URL.\n     */\n    cleanUrl(url) {\n      try {\n        url = url.split(\"?\")[0].split(\"#\")[0];\n        return new URL(url, window.location.href).href;\n      } catch (e) {\n        return url;\n      }\n    }\n    /**\n     * Fetches external stylesheet links from known font providers, retrieves their CSS,\n     * parses them into in-memory CSSStyleSheet objects, and extracts font-family/font-face\n     * information into a structured object.\n     *\n     * @async\n     * @function externalStylesheetsDoc\n     * @returns {Promise<{styleSheets: CSSStyleSheet[], fontPairs: Object}>} An object containing:\n     *   - styleSheets: Array of parsed CSSStyleSheet objects (not attached to the DOM).\n     *   - fontPairs: An object mapping font URLs to arrays of font variation objects\n     *     ({family, weight, style}).\n     *\n     * @example\n     * const { styleSheets, fontPairs } = await externalStylesheetsDoc();\n     * this.logger.logMessage(fontPairs);\n     */\n    async externalStylesheetsDoc() {\n      function generateFontPairsFromStyleSheets(styleSheetsArray) {\n        const fontPairs = {};\n        function _extractFirstUrlFromSrc(srcValue) {\n          if (!srcValue) return null;\n          const urlMatch = srcValue.match(/url\\s*\\(\\s*(['\"]?)(.+?)\\1\\s*\\)/);\n          return urlMatch ? urlMatch[2] : null;\n        }\n        function _cleanFontFamilyName(fontFamilyValue) {\n          if (!fontFamilyValue) return \"\";\n          return fontFamilyValue.replace(/^['\"]+|['\"]+$/g, \"\").trim();\n        }\n        if (!styleSheetsArray || !Array.isArray(styleSheetsArray)) {\n          console.warn(\n            \"generateFontPairsFromStyleSheets: Input is not a valid array. Received:\",\n            styleSheetsArray\n          );\n          return fontPairs;\n        }\n        if (styleSheetsArray.length === 0) {\n          return fontPairs;\n        }\n        styleSheetsArray.forEach((sheet) => {\n          if (sheet && sheet.cssRules) {\n            try {\n              for (const rule of sheet.cssRules) {\n                if (rule.type === CSSRule.FONT_FACE_RULE) {\n                  const cssFontFaceRule = rule;\n                  const fontFamily = _cleanFontFamilyName(\n                    cssFontFaceRule.style.getPropertyValue(\"font-family\")\n                  );\n                  const fontWeight = cssFontFaceRule.style.getPropertyValue(\"font-weight\") || \"normal\";\n                  const fontStyle = cssFontFaceRule.style.getPropertyValue(\"font-style\") || \"normal\";\n                  const src = cssFontFaceRule.style.getPropertyValue(\"src\");\n                  const fontUrl = _extractFirstUrlFromSrc(src);\n                  if (fontFamily && fontUrl) {\n                    const variation = {\n                      family: fontFamily,\n                      weight: fontWeight,\n                      style: fontStyle\n                    };\n                    if (!fontPairs[fontUrl]) fontPairs[fontUrl] = [];\n                    const variationExists = fontPairs[fontUrl].some(\n                      (v) => v.family === variation.family && v.weight === variation.weight && v.style === variation.style\n                    );\n                    if (!variationExists) fontPairs[fontUrl].push(variation);\n                  }\n                }\n              }\n            } catch (e) {\n              console.warn(\n                \"Error processing CSS rules from a stylesheet:\",\n                e,\n                sheet\n              );\n            }\n          } else if (sheet && !sheet.cssRules) {\n            console.warn(\n              \"Skipping a stylesheet as its cssRules are not accessible or it is empty:\",\n              sheet\n            );\n          }\n        });\n        return fontPairs;\n      }\n      const links = [\n        ...document.querySelectorAll('link[rel=\"stylesheet\"]')\n      ].filter((link) => {\n        try {\n          const linkUrl = new URL(link.href);\n          const currentUrl = new URL(window.location.href);\n          if (linkUrl.origin === currentUrl.origin) {\n            return false;\n          }\n          return !this.isUrlExcludedFromExternalProcessing(link.href);\n        } catch (e) {\n          return false;\n        }\n      });\n      if (links.length === 0) {\n        this.logger.logMessage(\"No external CSS links found to process.\");\n        return {\n          // Consistent return structure\n          styleSheets: [],\n          // The retrievable CSSStyleSheet objects\n          fontPairs: {}\n          // Processed data from these sheets\n        };\n      }\n      const fetchedCssPromises = links.map(\n        (linkElement) => fetch(linkElement.href, { mode: \"cors\" }).then((response) => {\n          if (response.ok) {\n            return response.text();\n          }\n          console.warn(\n            `Failed to fetch external CSS from ${linkElement.href}: ${response.status} ${response.statusText}`\n          );\n          return null;\n        }).catch((error) => {\n          console.error(\n            `Network error fetching external CSS from ${linkElement.href}:`,\n            error\n          );\n          return null;\n        })\n      );\n      const cssTexts = await Promise.all(fetchedCssPromises);\n      const temporaryStyleSheets = [];\n      cssTexts.forEach((txt) => {\n        if (txt && txt.trim() !== \"\") {\n          try {\n            const sheet = new CSSStyleSheet();\n            sheet.replaceSync(txt);\n            temporaryStyleSheets.push(sheet);\n          } catch (error) {\n            console.error(\n              \"Could not parse fetched CSS into a stylesheet:\",\n              error,\n              `\nCSS (first 200 chars): ${txt.substring(0, 200)}...`\n            );\n          }\n        }\n      });\n      if (temporaryStyleSheets.length > 0) {\n        this.logger.logMessage(\n          `[Beacon] ${temporaryStyleSheets.length} stylesheet(s) fetched and parsed into CSSStyleSheet objects.`\n        );\n      } else {\n        this.logger.logMessage(\n          \"[Beacon] No stylesheets were successfully parsed from the fetched CSS.\"\n        );\n      }\n      const processedFontPairs = generateFontPairsFromStyleSheets(temporaryStyleSheets);\n      return {\n        styleSheets: temporaryStyleSheets,\n        fontPairs: processedFontPairs\n      };\n    }\n    /**\n     * Asynchronously initializes and parses external font stylesheets.\n     * \n     * Fetches external font stylesheets and font pairs using `externalStylesheetsDoc`,\n     * then stores the parsed results in `externalParsedSheets` and `externalParsedPairs`.\n     * Logs the process and handles errors by resetting `externalParsedSheets` to an empty array.\n     * \n     * @async\n     * @returns {Promise<void>} Resolves when external font stylesheets have been initialized.\n     */\n    async _initializeExternalFontSheets() {\n      this.logger.logMessage(\"Initializing external font stylesheets...\");\n      try {\n        const result = await this.externalStylesheetsDoc();\n        this.externalParsedSheets = result.styleSheets || [];\n        this.externalParsedPairs = result.fontPairs || [];\n        this.logger.logMessage(\n          `Successfully parsed ${this.externalParsedSheets.length} external font stylesheets.`\n        );\n      } catch (error) {\n        this.logger.logMessage(\n          \"Error initializing external font stylesheets:\",\n          error\n        );\n        this.externalParsedSheets = [];\n      }\n    }\n    /**\n     * Retrieves a map of network-loaded fonts.\n     * \n     * This method uses the Performance API to get all resource entries, filters out\n     * the ones that match the font file regex, and maps them to their cleaned URLs.\n     * \n     * @returns {Map} A map where each key is a cleaned URL of a font file and\n     *                each value is the original URL of the font file.\n     */\n    getNetworkLoadedFonts() {\n      return new Map(\n        window.performance.getEntriesByType(\"resource\").filter((resource) => this.FONT_FILE_REGEX.test(resource.name)).map((resource) => [this.cleanUrl(resource.name), resource.name])\n      );\n    }\n    /**\n     * Retrieves font-face rules from stylesheets.\n     * \n     * This method scans all stylesheets loaded on the page and collects\n     * font-face rules, including their source URLs, font families, weights,\n     * and styles. It returns an object containing the collected font data.\n     * \n     * @returns {Promise<Object>} An object mapping font families to their respective\n     *                  URLs and variations.\n     */\n    async getFontFaceRules() {\n      const stylesheetFonts = {};\n      const processedUrls = /* @__PURE__ */ new Set();\n      const processFontFaceRule = (rule, baseHref = null) => {\n        const src = rule.style.getPropertyValue(\"src\");\n        const fontFamily = rule.style.getPropertyValue(\"font-family\").replace(/['\"]/g, \"\").trim();\n        const weight = rule.style.getPropertyValue(\"font-weight\") || \"400\";\n        const style = rule.style.getPropertyValue(\"font-style\") || \"normal\";\n        if (!stylesheetFonts[fontFamily]) {\n          stylesheetFonts[fontFamily] = { urls: [], variations: /* @__PURE__ */ new Set() };\n        }\n        const extractFirstUrlFromSrc = (srcValue) => {\n          if (!srcValue) return null;\n          const urlMatch = srcValue.match(/url\\s*\\(\\s*(['\"]?)(.+?)\\1\\s*\\)/);\n          return urlMatch ? urlMatch[2] : null;\n        };\n        const firstUrl = extractFirstUrlFromSrc(src);\n        if (firstUrl) {\n          let rawUrl = firstUrl;\n          if (baseHref) {\n            rawUrl = new URL(rawUrl, baseHref).href;\n          }\n          const normalized = this.cleanUrl(rawUrl);\n          if (!stylesheetFonts[fontFamily].urls.includes(normalized)) {\n            stylesheetFonts[fontFamily].urls.push(normalized);\n            stylesheetFonts[fontFamily].variations.add(\n              JSON.stringify({ weight, style })\n            );\n          }\n        }\n      };\n      const processImportRule = async (rule) => {\n        try {\n          const importUrl = rule.href;\n          if (this.isUrlExcludedFromExternalProcessing(importUrl)) {\n            return;\n          }\n          if (processedUrls.has(importUrl)) {\n            return;\n          }\n          processedUrls.add(importUrl);\n          const response = await fetch(importUrl, { mode: \"cors\" });\n          if (!response.ok) {\n            this.logger.logMessage(`Failed to fetch @import CSS: ${response.status}`);\n            return;\n          }\n          const cssText = await response.text();\n          const tempSheet = new CSSStyleSheet();\n          tempSheet.replaceSync(cssText);\n          Array.from(tempSheet.cssRules || []).forEach((importedRule) => {\n            if (importedRule instanceof CSSFontFaceRule) {\n              processFontFaceRule(importedRule, importUrl);\n            }\n          });\n        } catch (error) {\n          this.logger.logMessage(`Error processing @import rule: ${error.message}`);\n        }\n      };\n      const processSheet = async (sheet) => {\n        try {\n          const rules = Array.from(sheet.cssRules || []);\n          for (const rule of rules) {\n            if (rule instanceof CSSFontFaceRule) {\n              processFontFaceRule(rule, sheet.href);\n            } else if (rule instanceof CSSImportRule) {\n              if (rule.styleSheet) {\n                await processSheet(rule.styleSheet);\n              } else {\n                await processImportRule(rule);\n              }\n            } else if (rule.styleSheet) {\n              await processSheet(rule.styleSheet);\n            }\n          }\n        } catch (e) {\n          if (e.name === \"SecurityError\" && sheet.href) {\n            if (this.isUrlExcludedFromExternalProcessing(sheet.href)) {\n              return;\n            }\n            if (processedUrls.has(sheet.href)) {\n              return;\n            }\n            processedUrls.add(sheet.href);\n            try {\n              const response = await fetch(sheet.href, { mode: \"cors\" });\n              if (response.ok) {\n                const cssText = await response.text();\n                const tempSheet = new CSSStyleSheet();\n                tempSheet.replaceSync(cssText);\n                Array.from(tempSheet.cssRules || []).forEach((rule) => {\n                  if (rule instanceof CSSFontFaceRule) {\n                    processFontFaceRule(rule, sheet.href);\n                  }\n                });\n                const importRegex = /@import\\s+url\\(['\"]?([^'\")]+)['\"]?\\);?/g;\n                let importMatch;\n                while ((importMatch = importRegex.exec(cssText)) !== null) {\n                  const importUrl = new URL(importMatch[1], sheet.href).href;\n                  if (this.isUrlExcludedFromExternalProcessing(importUrl)) {\n                    continue;\n                  }\n                  if (processedUrls.has(importUrl)) {\n                    continue;\n                  }\n                  processedUrls.add(importUrl);\n                  try {\n                    const importResponse = await fetch(importUrl, { mode: \"cors\" });\n                    if (importResponse.ok) {\n                      const importCssText = await importResponse.text();\n                      const tempImportSheet = new CSSStyleSheet();\n                      tempImportSheet.replaceSync(importCssText);\n                      Array.from(tempImportSheet.cssRules || []).forEach((importedRule) => {\n                        if (importedRule instanceof CSSFontFaceRule) {\n                          processFontFaceRule(importedRule, importUrl);\n                        }\n                      });\n                    }\n                  } catch (importError) {\n                    this.logger.logMessage(`Error fetching @import ${importUrl}: ${importError.message}`);\n                  }\n                }\n              }\n            } catch (fetchError) {\n              this.logger.logMessage(`Error fetching stylesheet ${sheet.href}: ${fetchError.message}`);\n            }\n          } else {\n            this.logger.logMessage(`Error processing stylesheet: ${e.message}`);\n          }\n        }\n      };\n      const sheets = Array.from(document.styleSheets);\n      for (const sheet of sheets) {\n        await processSheet(sheet);\n      }\n      const inlineStyleElements = document.querySelectorAll(\"style\");\n      for (const styleElement of inlineStyleElements) {\n        const cssText = styleElement.textContent || styleElement.innerHTML || \"\";\n        const importRegex = /@import\\s+url\\s*\\(\\s*['\"]?([^'\")]+)['\"]?\\s*\\)\\s*;?/g;\n        let importMatch;\n        while ((importMatch = importRegex.exec(cssText)) !== null) {\n          const importUrl = importMatch[1];\n          if (this.isUrlExcludedFromExternalProcessing(importUrl)) {\n            continue;\n          }\n          if (processedUrls.has(importUrl)) {\n            continue;\n          }\n          processedUrls.add(importUrl);\n          try {\n            const response = await fetch(importUrl, { mode: \"cors\" });\n            if (response.ok) {\n              const importCssText = await response.text();\n              const tempSheet = new CSSStyleSheet();\n              tempSheet.replaceSync(importCssText);\n              Array.from(tempSheet.cssRules || []).forEach((importedRule) => {\n                if (importedRule instanceof CSSFontFaceRule) {\n                  processFontFaceRule(importedRule, importUrl);\n                }\n              });\n            }\n          } catch (importError) {\n            this.logger.logMessage(`Error fetching inline @import ${importUrl}: ${importError.message}`);\n          }\n        }\n      }\n      Object.values(stylesheetFonts).forEach((fontData) => {\n        fontData.variations = Array.from(fontData.variations).map((v) => JSON.parse(v));\n      });\n      return stylesheetFonts;\n    }\n    /**\n     * Checks if an element is above the fold (visible in the viewport without scrolling).\n     * \n     * @param {Element} element - The element to check.\n     * @returns {boolean} True if the element is above the fold, false otherwise.\n     */\n    isElementAboveFold(element) {\n      if (!this.isElementVisible(element)) return false;\n      const rect = element.getBoundingClientRect();\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      const elementTop = rect.top + scrollTop;\n      const foldPosition = window.innerHeight || document.documentElement.clientHeight;\n      return elementTop <= foldPosition;\n    }\n    /**\n     * Checks if an element can be processed for font analysis.\n     * \n     * This method combines checks for whether an element can be styled with font-family\n     * and whether it is above the fold, providing a single method to determine if an\n     * element should be processed during font analysis.\n     * \n     * @param {Element} element - The element to check.\n     * @returns {boolean} True if the element can be processed, false otherwise.\n     */\n    canElementBeProcessed(element) {\n      return this.canElementBeStyledWithFontFamily(element) && this.isElementAboveFold(element);\n    }\n    /**\n     * Initiates the process of analyzing and summarizing font usage on the page.\n     * This method fetches network-loaded fonts, stylesheet fonts, and external font pairs.\n     * It then processes each element on the page to determine which fonts are used above the fold.\n     * The results are summarized and logged.\n     * \n     * @returns {Promise<void>} A promise that resolves when the analysis is complete.\n     */\n    async run() {\n      await document.fonts.ready;\n      await this._initializeExternalFontSheets();\n      const networkLoadedFonts = this.getNetworkLoadedFonts();\n      const stylesheetFonts = await this.getFontFaceRules();\n      const hostedFonts = /* @__PURE__ */ new Map();\n      const externalFontsResults = await this.processExternalFonts(this.externalParsedPairs);\n      const elements = Array.from(document.getElementsByTagName(\"*\")).filter((el) => this.canElementBeProcessed(el));\n      elements.forEach((element) => {\n        const processElementFont = (style, pseudoElement = null) => {\n          if (!style || !this.isElementVisible(element)) return;\n          const fontFamily = style.fontFamily.split(\",\")[0].replace(/['\"]+/g, \"\").trim();\n          const hasContent = pseudoElement ? style.content !== \"none\" && style.content !== '\"\"' : element.textContent.trim();\n          if (hasContent && stylesheetFonts[fontFamily]) {\n            let urls = stylesheetFonts[fontFamily].urls;\n            if (!this.isExcluded(fontFamily, urls) && !hostedFonts.has(fontFamily)) {\n              hostedFonts.set(fontFamily, {\n                elements: /* @__PURE__ */ new Set(),\n                urls,\n                variations: stylesheetFonts[fontFamily].variations\n              });\n              hostedFonts.get(fontFamily).elements.add(element);\n            }\n          }\n        };\n        try {\n          processElementFont(window.getComputedStyle(element));\n          [\"::before\", \"::after\"].forEach((pseudo) => {\n            processElementFont(window.getComputedStyle(element, pseudo), pseudo);\n          });\n        } catch (e) {\n          this.logger.logMessage(\"Error processing element:\", e);\n        }\n      });\n      const aboveTheFoldFonts = this.summarizeMatches(externalFontsResults, hostedFonts, networkLoadedFonts);\n      if (!Object.keys(aboveTheFoldFonts.allFonts).length && !Object.keys(aboveTheFoldFonts.externalFonts).length && !Object.keys(aboveTheFoldFonts.hostedFonts).length) {\n        this.logger.logMessage(\"No fonts found above the fold.\");\n        return;\n      }\n      this.logger.logMessage(\"Above the fold fonts:\", aboveTheFoldFonts);\n      this.aboveTheFoldFonts = [...new Set(Object.values(aboveTheFoldFonts.allFonts).flatMap((font) => font.variations.map((variation) => variation.url)))];\n    }\n    /**\n     * Summarizes all font matches found on the page\n     * Creates a comprehensive object containing font usage data\n     *\n     * @param {Object} externalFontsResults - Results from External Fonts analysis\n     * @param {Map} hostedFonts - Map of hosted (non-External) fonts found\n     * @param {Map} networkLoadedFonts - Map of all font files loaded via network\n     * @returns {Object} Complete analysis of font usage including locations and counts\n     */\n    summarizeMatches(externalFontsResults, hostedFonts, networkLoadedFonts) {\n      const allFonts = {};\n      const hostedFontsResults = {};\n      if (hostedFonts.size > 0) {\n        hostedFonts.forEach((data, fontFamily) => {\n          if (data.variations) {\n            const elements = Array.from(data.elements);\n            const aboveElements = elements.filter((el) => this.isElementAboveFold(el));\n            const belowElements = elements.filter((el) => !this.isElementAboveFold(el));\n            data.variations.forEach((variation) => {\n              let matchingUrl = null;\n              for (const styleUrl of data.urls) {\n                const normalizedStyleUrl = this.cleanUrl(styleUrl);\n                if (networkLoadedFonts.has(normalizedStyleUrl)) {\n                  matchingUrl = networkLoadedFonts.get(normalizedStyleUrl);\n                  break;\n                }\n              }\n              if (matchingUrl) {\n                if (!allFonts[fontFamily]) {\n                  allFonts[fontFamily] = {\n                    type: \"hosted\",\n                    variations: [],\n                    elementCount: {\n                      aboveFold: aboveElements.length,\n                      belowFold: belowElements.length,\n                      total: elements.length\n                    },\n                    urlCount: {\n                      aboveFold: /* @__PURE__ */ new Set(),\n                      belowFold: /* @__PURE__ */ new Set()\n                    }\n                  };\n                }\n                allFonts[fontFamily].variations.push({\n                  weight: variation.weight,\n                  style: variation.style,\n                  url: matchingUrl,\n                  elementCount: {\n                    aboveFold: aboveElements.length,\n                    belowFold: belowElements.length,\n                    total: elements.length\n                  }\n                });\n                if (aboveElements.length > 0) {\n                  allFonts[fontFamily].urlCount.aboveFold.add(matchingUrl);\n                }\n                if (belowElements.length > 0) {\n                  allFonts[fontFamily].urlCount.belowFold.add(matchingUrl);\n                }\n              }\n            });\n            if (allFonts[fontFamily]) {\n              hostedFontsResults[fontFamily] = {\n                variations: allFonts[fontFamily].variations,\n                elementCount: { ...allFonts[fontFamily].elementCount },\n                urlCount: { ...allFonts[fontFamily].urlCount }\n              };\n            }\n          }\n        });\n      }\n      if (Object.keys(externalFontsResults).length > 0) {\n        Object.entries(externalFontsResults).forEach(([url, data]) => {\n          const aboveElements = Array.from(data.elements).filter((el) => this.isElementAboveFold(el));\n          const belowElements = Array.from(data.elements).filter((el) => !this.isElementAboveFold(el));\n          if (data.elementCount.aboveFold > 0 || aboveElements.length > 0) {\n            data.variations.forEach((variation) => {\n              if (!allFonts[variation.family]) {\n                allFonts[variation.family] = {\n                  type: \"external\",\n                  variations: [],\n                  // Track element counts at font family level\n                  elementCount: {\n                    aboveFold: 0,\n                    belowFold: 0,\n                    total: 0\n                  },\n                  // Track unique URLs used in each fold location\n                  urlCount: {\n                    aboveFold: /* @__PURE__ */ new Set(),\n                    belowFold: /* @__PURE__ */ new Set()\n                  }\n                };\n              }\n              allFonts[variation.family].variations.push({\n                weight: variation.weight,\n                style: variation.style,\n                url,\n                elementCount: {\n                  aboveFold: aboveElements.length,\n                  belowFold: belowElements.length,\n                  total: data.elements.length\n                }\n              });\n              allFonts[variation.family].elementCount.aboveFold += aboveElements.length;\n              allFonts[variation.family].elementCount.belowFold += belowElements.length;\n              allFonts[variation.family].elementCount.total += data.elements.length;\n              if (aboveElements.length > 0) {\n                allFonts[variation.family].urlCount.aboveFold.add(url);\n              }\n              if (belowElements.length > 0) {\n                allFonts[variation.family].urlCount.belowFold.add(url);\n              }\n            });\n          }\n        });\n      }\n      Object.values(allFonts).forEach((font) => {\n        font.urlCount = {\n          aboveFold: font.urlCount.aboveFold.size,\n          belowFold: font.urlCount.belowFold.size,\n          total: (/* @__PURE__ */ new Set([...font.urlCount.aboveFold, ...font.urlCount.belowFold])).size\n        };\n      });\n      Object.values(hostedFontsResults).forEach((font) => {\n        if (font.urlCount.aboveFold instanceof Set) {\n          font.urlCount = {\n            aboveFold: font.urlCount.aboveFold.size,\n            belowFold: font.urlCount.belowFold.size,\n            total: (/* @__PURE__ */ new Set([...font.urlCount.aboveFold, ...font.urlCount.belowFold])).size\n          };\n        }\n      });\n      return {\n        externalFonts: Object.fromEntries(\n          Object.entries(externalFontsResults).filter(\n            (entry) => entry[1].elementCount.aboveFold > 0\n          )\n        ),\n        hostedFonts: hostedFontsResults,\n        allFonts\n      };\n    }\n    /**\n     * Processes external font pairs to identify their usage on the page.\n     * \n     * This method iterates through all elements on the page, checks if they are above the fold,\n     * and determines the font information for each element. It then matches the font information\n     * with the provided external font pairs to identify which fonts are used and where.\n     * \n     * @param {Object} fontPairs - An object where each key is a URL and the value is an array of font variations.\n     * @returns {Promise<Object>} A promise that resolves to an object where each key is a URL and the value is an object containing information about the elements using that font.\n     */\n    async processExternalFonts(fontPairs) {\n      const matches = /* @__PURE__ */ new Map();\n      const elements = Array.from(document.getElementsByTagName(\"*\")).filter((el) => this.canElementBeProcessed(el));\n      const fontMap = /* @__PURE__ */ new Map();\n      Object.entries(fontPairs).forEach(([url, variations]) => {\n        variations.forEach((variation) => {\n          const key = `${variation.family}|${variation.weight}|${variation.style}`;\n          fontMap.set(key, { url, ...variation });\n        });\n      });\n      const getFontInfoForElement = (style) => {\n        const family = style.fontFamily.split(\",\")[0].replace(/['\"]+/g, \"\").trim();\n        const weight = style.fontWeight;\n        const fontStyle = style.fontStyle;\n        const key = `${family}|${weight}|${fontStyle}`;\n        let fontInfo = fontMap.get(key);\n        if (!fontInfo && weight !== \"400\") {\n          const fallbackKey = `${family}|400|${fontStyle}`;\n          fontInfo = fontMap.get(fallbackKey);\n        }\n        return fontInfo;\n      };\n      elements.forEach((element) => {\n        if (element.textContent.trim()) {\n          const style = window.getComputedStyle(element);\n          const fontInfo = getFontInfoForElement(style);\n          if (fontInfo) {\n            if (!this.isExcluded(fontInfo.family, [fontInfo.url]) && !matches.has(fontInfo.url)) {\n              matches.set(fontInfo.url, {\n                elements: /* @__PURE__ */ new Set(),\n                variations: /* @__PURE__ */ new Set()\n              });\n              matches.get(fontInfo.url).elements.add(element);\n              matches.get(fontInfo.url).variations.add(JSON.stringify({\n                family: fontInfo.family,\n                weight: fontInfo.weight,\n                style: fontInfo.style\n              }));\n            }\n          }\n        }\n        [\"::before\", \"::after\"].forEach((pseudo) => {\n          const pseudoStyle = window.getComputedStyle(element, pseudo);\n          if (pseudoStyle.content !== \"none\" && pseudoStyle.content !== '\"\"') {\n            const fontInfo = getFontInfoForElement(pseudoStyle);\n            if (fontInfo) {\n              if (!this.isExcluded(fontInfo.family, [fontInfo.url]) && !matches.has(fontInfo.url)) {\n                matches.set(fontInfo.url, {\n                  elements: /* @__PURE__ */ new Set(),\n                  variations: /* @__PURE__ */ new Set()\n                });\n                matches.get(fontInfo.url).elements.add(element);\n                matches.get(fontInfo.url).variations.add(JSON.stringify({\n                  family: fontInfo.family,\n                  weight: fontInfo.weight,\n                  style: fontInfo.style\n                }));\n              }\n            }\n          }\n        });\n      });\n      return Object.fromEntries(\n        Array.from(matches.entries()).map(([url, data]) => [\n          url,\n          {\n            elementCount: {\n              aboveFold: Array.from(data.elements).filter((el) => this.isElementAboveFold(el)).length,\n              total: data.elements.size\n            },\n            variations: Array.from(data.variations).map((v) => JSON.parse(v)),\n            elements: Array.from(data.elements)\n          }\n        ])\n      );\n    }\n    /**\n     * Retrieves the results of the font analysis, specifically the fonts used above the fold.\n     * This method returns an array containing the URLs of the fonts used above the fold.\n     * \n     * @returns {Array<string>} An array of URLs of the fonts used above the fold.\n     */\n    getResults() {\n      return this.aboveTheFoldFonts;\n    }\n  };\n  var BeaconPreloadFonts_default = BeaconPreloadFonts;\n\n  // src/BeaconPreconnectExternalDomain.js\n  var BeaconPreconnectExternalDomain = class {\n    constructor(config, logger) {\n      this.logger = logger;\n      this.result = [];\n      this.excludedPatterns = config.preconnect_external_domain_exclusions;\n      this.eligibleElements = config.preconnect_external_domain_elements;\n      this.matchedItems = /* @__PURE__ */ new Set();\n      this.excludedItems = /* @__PURE__ */ new Set();\n    }\n    /**\n     * Initiates the process of identifying and logging external domains that require preconnection.\n     * This method queries the document for eligible elements, processes each element to determine\n     * if it should be preconnected, and logs the results.\n     */\n    async run() {\n      const elements = document.querySelectorAll(\n        `${this.eligibleElements.join(\", \")}[src], ${this.eligibleElements.join(\", \")}[href], ${this.eligibleElements.join(\", \")}[rel], ${this.eligibleElements.join(\", \")}[type]`\n      );\n      elements.forEach((el) => this.processElement(el));\n      this.logger.logMessage({ matchedItems: this.getMatchedItems(), excludedItems: Array.from(this.excludedItems) });\n    }\n    /**\n     * Processes a single element to determine if it should be preconnected.\n     * \n     * This method checks if the element is excluded based on attribute or domain rules.\n     * If not excluded, it checks if the element's URL is an external domain and adds it to the list of matched items.\n     * \n     * @param {Element} el - The element to process.\n     */\n    processElement(el) {\n      try {\n        const url = new URL(el.src || el.href || \"\", location.href);\n        if (this.isExcluded(el)) {\n          this.excludedItems.add(this.createExclusionObject(url, el));\n          return;\n        }\n        if (this.isExternalDomain(url)) {\n          this.matchedItems.add(`${url.hostname}-${el.tagName.toLowerCase()}`);\n          this.result = [...new Set(this.result.concat(url.origin))];\n        }\n      } catch (e) {\n        this.logger.logMessage(e);\n      }\n    }\n    /**\n     * Checks if an element is excluded based on exclusions patterns.\n     * \n     * This method iterates through the excludedPatterns array and checks if any pattern matches any of the element's attribute or values.\n     * If a match is found, it returns true, indicating the element is excluded.\n     * \n     * @param {Element} el - The element to check.\n     * @returns {boolean} True if the element is excluded by an attribute rule, false otherwise.\n     */\n    isExcluded(el) {\n      const outerHTML = el.outerHTML.substring(0, el.outerHTML.indexOf(\">\") + 1);\n      return this.excludedPatterns.some(\n        (pattern) => outerHTML.includes(pattern)\n      );\n    }\n    /**\n     * Checks if a URL is excluded based on domain rules.\n     * \n     * This method iterates through the excludedPatterns array and checks if any pattern matches the URL's hostname.\n     * If a match is found, it returns true, indicating the URL is excluded.\n     * \n     * @param {URL} url - The URL to check.\n     * @returns {boolean} True if the URL is excluded by a domain rule, false otherwise.\n     */\n    isExcludedByDomain(url) {\n      return this.excludedPatterns.some(\n        (pattern) => pattern.type === \"domain\" && url.hostname.includes(pattern.value)\n      );\n    }\n    /**\n     * Checks if a URL is from an external domain.\n     * \n     * This method compares the hostname of the given URL with the hostname of the current location.\n     * If they are not the same, it indicates the URL is from an external domain.\n     * \n     * @param {URL} url - The URL to check.\n     * @returns {boolean} True if the URL is from an external domain, false otherwise.\n     */\n    isExternalDomain(url) {\n      return url.hostname !== location.hostname && url.hostname;\n    }\n    /**\n     * Creates an exclusion object based on the URL, element.\n     * \n     * @param {URL} url - The URL to create the exclusion object for.\n     * @param {Element} el - The element to create the exclusion object for.\n     * @returns {Object} An object with the URL's hostname, the element's tag name, and the reason.\n     */\n    createExclusionObject(url, el) {\n      return { domain: url.hostname, elementType: el.tagName.toLowerCase() };\n    }\n    /**\n     * Returns an array of matched items, each item split into its domain and element type.\n     * \n     * This method iterates through the matchedItems set, splits each item into its domain and element type using the last hyphen as a delimiter,\n     * and returns an array of these split items.\n     * \n     * @returns {Array} An array of arrays, each containing a domain and an element type.\n     */\n    getMatchedItems() {\n      return Array.from(this.matchedItems).map((item) => {\n        const lastHyphenIndex = item.lastIndexOf(\"-\");\n        return [\n          item.substring(0, lastHyphenIndex),\n          // Domain\n          item.substring(lastHyphenIndex + 1)\n          // Element type\n        ];\n      });\n    }\n    /**\n     * Returns the array of unique domain names that were found to be external.\n     * \n     * This method returns the result array, which contains a list of unique domain names that were identified as external during the analysis process.\n     * \n     * @returns {Array} An array of unique domain names.\n     */\n    getResults() {\n      return this.result;\n    }\n  };\n  var BeaconPreconnectExternalDomain_default = BeaconPreconnectExternalDomain;\n\n  // src/Logger.js\n  var Logger = class {\n    constructor(enabled) {\n      this.enabled = enabled;\n    }\n    logMessage(label, msg = \"\") {\n      if (!this.enabled) {\n        return;\n      }\n      if (msg !== \"\") {\n        console.log(label, msg);\n        return;\n      }\n      console.log(label);\n    }\n    logColoredMessage(msg, color = \"green\") {\n      if (!this.enabled) {\n        return;\n      }\n      console.log(`%c${msg}`, `color: ${color};`);\n    }\n  };\n  var Logger_default = Logger;\n\n  // src/BeaconManager.js\n  var BeaconManager = class {\n    constructor(config) {\n      this.config = config;\n      this.lcpBeacon = null;\n      this.lrcBeacon = null;\n      this.preloadFontsBeacon = null;\n      this.preconnectExternalDomainBeacon = null;\n      this.infiniteLoopId = null;\n      this.errorCode = \"\";\n      this.logger = new Logger_default(this.config.debug);\n    }\n    async init() {\n      this.scriptTimer = /* @__PURE__ */ new Date();\n      if (!await this._isValidPreconditions()) {\n        this._finalize();\n        return;\n      }\n      if (Utils_default.isPageScrolled()) {\n        this.logger.logMessage(\"Bailing out because the page has been scrolled\");\n        this._finalize();\n        return;\n      }\n      this.infiniteLoopId = setTimeout(() => {\n        this._handleInfiniteLoop();\n      }, 1e4);\n      const isGeneratedBefore = await this._getGeneratedBefore();\n      const shouldGenerateLcp = this.config.status.atf && (isGeneratedBefore === false || isGeneratedBefore.lcp === false);\n      const shouldGeneratelrc = this.config.status.lrc && (isGeneratedBefore === false || isGeneratedBefore.lrc === false);\n      const shouldGeneratePreloadFonts = this.config.status.preload_fonts && (isGeneratedBefore === false || isGeneratedBefore.preload_fonts === false);\n      const shouldGeneratePreconnectExternalDomain = this.config.status.preconnect_external_domain && (isGeneratedBefore === false || isGeneratedBefore.preconnect_external_domain === false);\n      if (shouldGenerateLcp) {\n        this.lcpBeacon = new BeaconLcp_default(this.config, this.logger);\n        await this.lcpBeacon.run();\n      } else {\n        this.logger.logMessage(\"Not running BeaconLcp because data is already available or feature is disabled\");\n      }\n      if (shouldGeneratelrc) {\n        this.lrcBeacon = new BeaconLrc_default(this.config, this.logger);\n        await this.lrcBeacon.run();\n      } else {\n        this.logger.logMessage(\"Not running BeaconLrc because data is already available or feature is disabled\");\n      }\n      if (shouldGeneratePreloadFonts) {\n        this.preloadFontsBeacon = new BeaconPreloadFonts_default(this.config, this.logger);\n        await this.preloadFontsBeacon.run();\n      } else {\n        this.logger.logMessage(\"Not running BeaconPreloadFonts because data is already available or feature is disabled\");\n      }\n      if (shouldGeneratePreconnectExternalDomain) {\n        this.preconnectExternalDomainBeacon = new BeaconPreconnectExternalDomain_default(this.config, this.logger);\n        await this.preconnectExternalDomainBeacon.run();\n      } else {\n        this.logger.logMessage(\"Not running BeaconPreconnectExternalDomain because data is already available or feature is disabled\");\n      }\n      if (shouldGenerateLcp || shouldGeneratelrc || shouldGeneratePreloadFonts || shouldGeneratePreconnectExternalDomain) {\n        this._saveFinalResultIntoDB();\n      } else {\n        this.logger.logMessage(\"Not saving results into DB as no beacon features ran.\");\n        this._finalize();\n      }\n    }\n    async _isValidPreconditions() {\n      const threshold = {\n        width: this.config.width_threshold,\n        height: this.config.height_threshold\n      };\n      if (Utils_default.isNotValidScreensize(this.config.is_mobile, threshold)) {\n        this.logger.logMessage(\"Bailing out because screen size is not acceptable\");\n        return false;\n      }\n      return true;\n    }\n    async _getGeneratedBefore() {\n      if (!Utils_default.isPageCached()) {\n        return false;\n      }\n      let data_check = new FormData();\n      data_check.append(\"action\", \"rocket_check_beacon\");\n      data_check.append(\"rocket_beacon_nonce\", this.config.nonce);\n      data_check.append(\"url\", this.config.url);\n      data_check.append(\"is_mobile\", this.config.is_mobile);\n      const beacon_data_response = await fetch(this.config.ajax_url, {\n        method: \"POST\",\n        credentials: \"same-origin\",\n        body: data_check\n      }).then((data) => data.json());\n      return beacon_data_response.data;\n    }\n    _saveFinalResultIntoDB() {\n      const results = {\n        lcp: this.lcpBeacon ? this.lcpBeacon.getResults() : null,\n        lrc: this.lrcBeacon ? this.lrcBeacon.getResults() : null,\n        preload_fonts: this.preloadFontsBeacon ? this.preloadFontsBeacon.getResults() : null,\n        preconnect_external_domain: this.preconnectExternalDomainBeacon ? this.preconnectExternalDomainBeacon.getResults() : null\n      };\n      const data = new FormData();\n      data.append(\"action\", \"rocket_beacon\");\n      data.append(\"rocket_beacon_nonce\", this.config.nonce);\n      data.append(\"url\", this.config.url);\n      data.append(\"is_mobile\", this.config.is_mobile);\n      data.append(\"status\", this._getFinalStatus());\n      data.append(\"results\", JSON.stringify(results));\n      fetch(this.config.ajax_url, {\n        method: \"POST\",\n        credentials: \"same-origin\",\n        body: data,\n        headers: {\n          \"wpr-saas-no-intercept\": true\n        }\n      }).then((response) => response.json()).then((data2) => {\n        this.logger.logMessage(data2.data.lcp);\n      }).catch((error) => {\n        this.logger.logMessage(error);\n      }).finally(() => {\n        this._finalize();\n      });\n    }\n    _getFinalStatus() {\n      if (\"\" !== this.errorCode) {\n        return this.errorCode;\n      }\n      const scriptTime = (/* @__PURE__ */ new Date() - this.scriptTimer) / 1e3;\n      if (10 <= scriptTime) {\n        return \"timeout\";\n      }\n      return \"success\";\n    }\n    _handleInfiniteLoop() {\n      this._saveFinalResultIntoDB();\n    }\n    _finalize() {\n      const beaconscript = document.querySelector('[data-name=\"wpr-wpr-beacon\"]');\n      beaconscript.setAttribute(\"beacon-completed\", \"true\");\n      clearTimeout(this.infiniteLoopId);\n    }\n  };\n  var BeaconManager_default = BeaconManager;\n\n  // src/BeaconEntryPoint.js\n  ((rocket_beacon_data) => {\n    if (!rocket_beacon_data) {\n      return;\n    }\n    const instance = new BeaconManager_default(rocket_beacon_data);\n    if (document.readyState !== \"loading\") {\n      setTimeout(() => {\n        instance.init();\n      }, rocket_beacon_data.delay);\n      return;\n    }\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      setTimeout(() => {\n        instance.init();\n      }, rocket_beacon_data.delay);\n    });\n  })(window.rocket_beacon_data);\n  var BeaconEntryPoint_default = BeaconManager_default;\n})();\n"], "mappings": "CAAC,IAAM,CAEL,IAAIA,EAAc,KAAM,CACtB,OAAO,gBAAiB,CACtB,OAAO,OAAO,YAAc,SAAS,gBAAgB,WACvD,CACA,OAAO,iBAAkB,CACvB,OAAO,OAAO,aAAe,SAAS,gBAAgB,YACxD,CACA,OAAO,qBAAqBC,EAAWC,EAAW,CAChD,MAAMC,EAAc,KAAK,eAAe,EAClCC,EAAe,KAAK,gBAAgB,EACpCC,EAAsBJ,IAAcE,EAAcD,EAAU,OAASE,EAAeF,EAAU,QAC9FI,EAAuB,CAACL,IAAcE,EAAcD,EAAU,OAASE,EAAeF,EAAU,QACtG,OAAOG,GAAuBC,CAChC,CACA,OAAO,cAAe,CACpB,MAAMC,EAAY,SAAS,gBAAgB,aAAe,SAAS,gBAAgB,YAAY,KAAO,SAAS,gBAAgB,YAAY,KAAO,GAClJ,OAAOA,GAAaA,EAAU,SAAS,eAAe,CACxD,CACA,OAAO,eAAeC,EAAM,CAC1B,OAAOA,EAAK,QAAU,GAAKA,EAAK,OAAS,GAAKA,EAAK,MAAQ,OAAO,aAAe,SAAS,gBAAgB,eAAiBA,EAAK,OAAS,OAAO,YAAc,SAAS,gBAAgB,YACzL,CACA,OAAO,gBAAiB,CACtB,OAAO,OAAO,YAAc,GAAK,SAAS,gBAAgB,UAAY,CACxE,CAYA,OAAO,iBAAiBC,EAAS,CAC/B,MAAMC,EAAQ,OAAO,iBAAiBD,CAAO,EACvCD,EAAOC,EAAQ,sBAAsB,EAI3C,MAHI,CAACC,GAGD,KAAK,mBAAmBD,CAAO,EAC1B,GAEF,EAAEC,EAAM,UAAY,QAAUA,EAAM,aAAe,UAAYA,EAAM,UAAY,KAAOF,EAAK,QAAU,GAAKA,EAAK,SAAW,EACrI,CAWA,OAAO,mBAAmBC,EAAS,CACjC,MAAMC,EAAQ,OAAO,iBAAiBD,CAAO,EAC7C,GAAI,CAACC,EACH,MAAO,GAET,MAAMC,EAAQD,EAAM,OAAS,GACvBE,EAASF,EAAM,QAAU,GAgB/B,MAfI,GAAAC,IAAU,eAGIA,EAAM,MAAM,gCAAgC,GAI5CA,EAAM,MAAM,kCAAkC,GAI/CA,EAAM,MAAM,mBAAmB,GAI5CC,EAAO,SAAS,YAAY,EAIlC,CACF,EACIC,EAAgBb,EAGhBc,EAAY,KAAM,CACpB,YAAYC,EAAQC,EAAQ,CAC1B,KAAK,OAASD,EACd,KAAK,kBAAoB,CAAC,EAC1B,KAAK,OAASC,CAChB,CACA,MAAM,KAAM,CACV,GAAI,CACF,MAAMC,EAAwB,KAAK,uBAAuB,GAAQ,EAC9DA,IACF,KAAK,8BAA8BA,CAAqB,EACxD,KAAK,4BAA4BA,CAAqB,EAE1D,OAASC,EAAK,CACZ,KAAK,UAAY,eACjB,KAAK,OAAO,WAAW,iBAAmBA,CAAG,CAC/C,CACF,CACA,uBAAuBC,EAAO,CAC5B,MAAMC,EAAc,SAAS,iBAAiB,KAAK,OAAO,QAAQ,EAClE,OAAIA,EAAY,QAAU,EACjB,CAAC,EAEkB,MAAM,KAAKA,CAAW,EACR,IAAKX,GAAY,CACzD,GAAcA,EAAQ,SAAS,YAAY,IAAvC,OAA0DA,EAAQ,cAAc,SAAS,YAAY,IAAzD,UAC9C,OAAO,KAET,IAAID,EACJ,GAAkBC,EAAQ,SAAS,YAAY,IAA3C,UAA8C,CAChD,MAAMY,EAAaZ,EAAQ,cAAc,KAAK,EAC9C,GAAIY,EACFb,EAAOa,EAAW,sBAAsB,MAExC,QAAO,IAEX,MACEb,EAAOC,EAAQ,sBAAsB,EAEvC,MAAO,CACL,QAAAA,EACA,KAAAD,CACF,CACF,CAAC,EAAE,OAAQc,GAASA,IAAS,IAAI,EAAE,OAAQA,GAClCA,EAAK,KAAK,MAAQ,GAAKA,EAAK,KAAK,OAAS,GAAKT,EAAc,eAAeS,EAAK,IAAI,GAAKT,EAAc,iBAAiBS,EAAK,OAAO,CAC7I,EAAE,IAAKA,IAAU,CAChB,KAAAA,EACA,KAAM,KAAK,gBAAgBA,EAAK,IAAI,EACpC,YAAa,KAAK,gBAAgBA,EAAK,OAAO,CAChD,EAAE,EAAE,KAAK,CAACC,EAAGC,IAAMA,EAAE,KAAOD,EAAE,IAAI,EAAE,MAAM,EAAGJ,CAAK,EAC7B,IAAKM,IAAe,CACvC,QAASA,EAAU,KAAK,QACxB,YAAaA,EAAU,WACzB,EAAE,CACJ,CACA,gBAAgBjB,EAAM,CACpB,MAAMkB,EAAe,KAAK,IAAIlB,EAAK,OAAQ,OAAO,YAAc,SAAS,gBAAgB,aAAeA,EAAK,IAAI,EAC3GmB,EAAgB,KAAK,IAAInB,EAAK,QAAS,OAAO,aAAe,SAAS,gBAAgB,cAAgBA,EAAK,GAAG,EACpH,OAAOkB,EAAeC,CACxB,CACA,gBAAgBlB,EAAS,CACvB,MAAMmB,EAAWnB,EAAQ,SAAS,YAAY,EACxCoB,EAAe,CACnB,KAAM,GACN,IAAK,GACL,OAAQ,GACR,MAAO,GACP,QAAS,CAAC,EACV,OAAQ,CAAC,EACT,YAAa,EACf,EACMC,EAAiB,2CACvB,GAAIF,IAAa,OAASnB,EAAQ,OAChCoB,EAAa,KAAO,aACpBA,EAAa,IAAMpB,EAAQ,IAC3BoB,EAAa,OAASpB,EAAQ,OAC9BoB,EAAa,MAAQpB,EAAQ,MAC7BoB,EAAa,YAAcpB,EAAQ,mBAC1BmB,IAAa,MACtBC,EAAa,KAAO,MACpBA,EAAa,IAAMpB,EAAQ,IAC3BoB,EAAa,YAAcpB,EAAQ,mBAC1BmB,IAAa,QAAS,CAC/BC,EAAa,KAAO,MACpB,MAAME,EAAStB,EAAQ,cAAc,QAAQ,EAC7CoB,EAAa,IAAMpB,EAAQ,SAAWsB,EAASA,EAAO,IAAM,IAC5DF,EAAa,YAAcA,EAAa,GAC1C,SAAWD,IAAa,MAAO,CAC7B,MAAMI,EAAevB,EAAQ,cAAc,OAAO,EAC9CuB,IACFH,EAAa,KAAO,MACpBA,EAAa,IAAMG,EAAa,aAAa,MAAM,GAAK,GACxDH,EAAa,YAAcA,EAAa,IAE5C,SAAWD,IAAa,UAAW,CACjCC,EAAa,KAAO,UACpB,MAAMI,EAAMxB,EAAQ,cAAc,KAAK,EACvCoB,EAAa,IAAMI,EAAMA,EAAI,IAAM,GACnCJ,EAAa,QAAU,MAAM,KAAKpB,EAAQ,iBAAiB,QAAQ,CAAC,EAAE,IAAKsB,IAAY,CACrF,OAAQA,EAAO,QAAU,GACzB,MAAOA,EAAO,OAAS,GACvB,KAAMA,EAAO,MAAQ,GACrB,MAAOA,EAAO,OAAS,EACzB,EAAE,CACJ,KAAO,CAEL,MAAMG,EAAW,CADM,OAAO,iBAAiBzB,EAAS,IAAI,EAE3C,iBAAiB,kBAAkB,EAClD,iBAAiBA,EAAS,QAAQ,EAAE,iBAAiB,kBAAkB,EACvE,iBAAiBA,EAAS,SAAS,EAAE,iBAAiB,kBAAkB,CAC1E,EAAE,OAAQ0B,GAASA,IAAS,MAAM,EAClC,GAAID,EAAS,SAAW,EACtB,OAAO,KAET,MAAME,EAAeF,EAAS,CAAC,EAK/B,GAJAL,EAAa,KAAO,SAChBO,EAAa,SAAS,YAAY,IACpCP,EAAa,KAAO,cAElB,CAACO,GAAgBA,IAAiB,IAAMA,EAAa,SAAS,YAAY,EAC5E,OAAO,KAET,MAAMC,EAAU,CAAC,GAAGD,EAAa,SAASN,CAAc,CAAC,EAKzD,GAJAD,EAAa,OAASQ,EAAQ,IAAKC,GAAMA,EAAE,CAAC,EAAI,CAAE,IAAKA,EAAE,CAAC,EAAE,KAAK,GAAKA,EAAE,CAAC,EAAI,IAAMA,EAAE,CAAC,EAAE,KAAK,EAAI,GAAI,EAAI,CAAC,CAAC,EACvGT,EAAa,OAAO,MAAOP,GAASA,EAAK,MAAQ,EAAE,IACrDO,EAAa,OAASQ,EAAQ,IAAKC,GAAMA,EAAE,CAAC,EAAI,CAAE,IAAKA,EAAE,CAAC,EAAE,KAAK,CAAE,EAAI,CAAC,CAAC,GAEvET,EAAa,OAAO,QAAU,EAChC,OAAO,KAELA,EAAa,OAAO,OAAS,IAC/BA,EAAa,IAAMA,EAAa,OAAO,CAAC,EAAE,IACtCA,EAAa,OAAS,eACxBA,EAAa,IAAMA,EAAa,QAGtC,CACA,OAAOA,CACT,CACA,8BAA8BU,EAAU,CACtC,MAAMC,EAAuBD,EAAS,KAAMjB,GACnCA,EAAK,cAAgB,OAASA,EAAK,YAAY,KAAOA,EAAK,YAAY,OAC/E,EACD,GAAI,CAACkB,EAAsB,CACzB,KAAK,OAAO,WAAW,yBAAyB,EAChD,KAAK,kBAAoB,CAAC,EAC1B,MACF,CACA,KAAK,kBAAoB,CAAC,CACxB,GAAGA,EAAqB,YACxB,MAAO,KACT,CAAC,CACH,CACA,4BAA4BD,EAAU,CACpCA,EAAS,QAAQ,CAAC,CAAE,QAAA9B,EAAS,YAAAgC,CAAY,IAAM,CACzC,KAAK,kBAAkBhC,CAAO,GAAK,CAACgC,GAGxC,KAAK,kBAAkB,KAAK,CAAE,GAAGA,EAAa,MAAO,gBAAiB,CAAC,CACzE,CAAC,CACH,CACA,kBAAkBC,EAAO,CACvB,MAAMD,EAAc,KAAK,gBAAgBC,CAAK,EAC9C,GAAID,IAAgB,KAClB,MAAO,GAET,MAAME,EAAiBF,EAAY,OAAS,OAASA,EAAY,OAAS,cAAgBA,EAAY,OAAS,QACzGG,EAAqBH,EAAY,OAAS,UAAYA,EAAY,OAAS,cAAgBA,EAAY,OAAS,UACtH,OAAQE,GAAkBC,IAAuB,KAAK,kBAAkB,KAAMtB,GAASA,EAAK,MAAQmB,EAAY,GAAG,CACrH,CACA,YAAa,CACX,OAAO,KAAK,iBACd,CACF,EACII,EAAoB/B,EAGpBgC,EAAY,KAAM,CACpB,YAAY/B,EAAQC,EAAQ,CAC1B,KAAK,OAASD,EACd,KAAK,OAASC,EACd,KAAK,mBAAqB,CAAC,CAC7B,CACA,MAAM,KAAM,CACV,GAAI,CACF,MAAM+B,EAAiB,KAAK,uBAAuB,EAC/CA,GACF,KAAK,iBAAiBA,CAAc,CAExC,OAAS7B,EAAK,CACZ,KAAK,UAAY,eACjB,KAAK,OAAO,WAAW,iBAAmBA,CAAG,CAC/C,CACF,CACA,wBAAyB,CACvB,MAAMqB,EAAW,SAAS,iBAAiB,6BAA6B,EAClES,EAAgB,KAAK,kBAAkB,EAC7C,OAAIT,EAAS,QAAU,EACd,CAAC,EAEY,MAAM,KAAKA,CAAQ,EAAE,OAAQ9B,GAC7C,KAAK,aAAaA,CAAO,EACpB,GAELuC,EAAc,SAASvC,CAAO,GAChC,KAAK,OAAO,kBAAkB,mCAAmCA,EAAQ,OAAO,GAAI,QAAQ,EACrF,IAEF,EACR,EACoB,IAAKA,IAAa,CACrC,QAAAA,EACA,MAAO,KAAK,iBAAiBA,CAAO,EACpC,SAAU,KAAK,oBAAoBA,CAAO,EAC1C,KAAM,KAAK,iBAAiBA,CAAO,CACrC,EAAE,CACJ,CACA,iBAAiBA,EAAS,CACxB,IAAIwC,EAAQ,EACRC,EAASzC,EAAQ,cACrB,KAAOyC,GACLD,IACAC,EAASA,EAAO,cAElB,OAAOD,CACT,CACA,oBAAoBxC,EAAS,CAC3B,MAAMD,EAAOC,EAAQ,sBAAsB,EACrC0C,EAAY,OAAO,aAAe,SAAS,gBAAgB,UACjE,OAAO,KAAK,IAAI,EAAG3C,EAAK,IAAM2C,EAAYtC,EAAc,gBAAgB,CAAC,CAC3E,CACA,aAAaJ,EAAS,CACpB,MAAM2C,EAAc,KAAK,OAAO,aAAe,CAAC,OAAO,EACvD,MAAI,CAAC3C,GAAW,CAACA,EAAQ,GAAW,GAC7B2C,EAAY,KAAMC,GAAQ5C,EAAQ,GAAG,YAAY,EAAE,SAAS4C,EAAI,YAAY,CAAC,CAAC,CACvF,CACA,mBAAmB5C,EAAS6C,EAAY,CACtC,GAAI,CAAC7C,EAAS,MAAO,GACrB,QAAS8C,EAAI,EAAGA,EAAID,EAAW,OAAQC,IAAK,CAC1C,KAAM,CAACC,EAAWC,CAAO,EAAIH,EAAWC,CAAC,EACnCG,EAAiBjD,EAAQ,aAAa+C,CAAS,EACrD,GAAIE,GAAkB,IAAI,OAAOD,EAAS,GAAG,EAAE,KAAKC,CAAc,EAChE,MAAO,EAEX,CACA,MAAO,EACT,CACA,kBAAkBjD,EAAS,CACzB,MAAMkD,EAAsB,CAAC,EACvBC,EAAgB,OAAO,iBAAiBnD,CAAO,EAE/CoD,EADe,CAAC,YAAa,cAAe,eAAgB,YAAY,EACzC,KAAMC,GAAW,WAAWF,EAAcE,CAAM,CAAC,EAAI,CAAC,EAE3F,OADgCD,GAAmBD,EAAc,oBAAsB,QAAUA,EAAc,oBAAsB,WAEnID,EAAoB,KAAK,CACvB,QAAAlD,EACA,UAAW,CACToD,GAAmB,kBACnBD,EAAc,oBAAsB,QAAU,0BAC9CA,EAAc,oBAAsB,UAAY,2BAClD,EAAE,OAAO,OAAO,CAClB,CAAC,EAEH,MAAM,KAAKnD,EAAQ,QAAQ,EAAE,QAASsD,GAAU,CAC9C,MAAMC,EAAa,OAAO,iBAAiBD,CAAK,EAE1CE,EADgB,CAAC,YAAa,cAAe,eAAgB,YAAY,EACpC,KAAMH,GAAW,WAAWE,EAAWF,CAAM,CAAC,EAAI,CAAC,GACvEG,GAAwBD,EAAW,WAAa,YAAcA,EAAW,WAAa,UAE3GL,EAAoB,KAAK,CACvB,QAASI,EACT,UAAW,CACTE,GAAwB,kBACxBD,EAAW,WAAa,YAAc,oBACtCA,EAAW,WAAa,SAAW,gBACrC,EAAE,OAAO,OAAO,CAClB,CAAC,CAEL,CAAC,EACML,CACT,CACA,iBAAiBpB,EAAU,CACzBA,EAAS,QAAQ,CAAC,CAAE,QAAA9B,EAAS,MAAAwC,EAAO,SAAAiB,EAAU,KAAAC,CAAK,IAAM,CAIvD,GAHI,KAAK,mBAAmB1D,EAAS,KAAK,OAAO,YAAc,CAAC,CAAC,GAGtC0D,IAAvB,mBACF,OAEF,MAAMC,EAAY,KAAK,kBAAkB3D,CAAO,EAChD,GAAI2D,EAAU,OAAS,EAAG,CACxB,KAAK,OAAO,WAAW,qCAAsCA,CAAS,EACtE,MACF,CACA,MAAMC,EAAgB5D,EAAQ,eAAiB,KAAK,oBAAoBA,EAAQ,aAAa,EAAI,KAAK,OAAO,eAAiByD,GAAY,KAAK,OAAO,cAChJvD,EAAQ0D,EAAgB,QAAUH,IAAa,EAAI,MAAQ,GACjE,KAAK,OAAO,kBAAkB,GAAG,IAAI,OAAOjB,CAAK,CAAC,GAAGxC,EAAQ,OAAO,YAAYwC,CAAK,oCAAoCiB,CAAQ,MAAOvD,CAAK,EAC7I,KAAK,OAAO,kBAAkB,GAAG,IAAI,OAAOsC,CAAK,CAAC,kBAAkBkB,CAAI,GAAIxD,CAAK,EACjF,KAAK,OAAO,kBAAkB,GAAG,IAAI,OAAOsC,CAAK,CAAC,6BAA6BxC,EAAQ,YAAY,GAAIE,CAAK,EACxG0D,IACF,KAAK,mBAAmB,KAAKF,CAAI,EACjC,KAAK,OAAO,WAAW,6BAA6BA,CAAI,EAAE,EAE9D,CAAC,CACH,CACA,UAAU1D,EAAS,CACjB,OAAIA,GAAWA,EAAQ,KAAO,GACrB,YAAYA,EAAQ,EAAE,KAExB,KAAK,iBAAiBA,CAAO,CACtC,CACA,iBAAiBA,EAAS,CACxB,GAAIA,IAAY,SAAS,KACvB,MAAO,aAET,MAAM6D,EAAW,KAAK,oBAAoB7D,CAAO,EACjD,MAAO,GAAG,KAAK,iBAAiBA,EAAQ,UAAU,CAAC,IAAIA,EAAQ,SAAS,YAAY,CAAC,IAAI6D,CAAQ,GACnG,CACA,oBAAoB7D,EAAS,CAC3B,IAAI8D,EAAM,EACNC,EAAU/D,EAAQ,uBACtB,KAAO+D,GACDA,EAAQ,WAAa/D,EAAQ,UAC/B8D,IAEFC,EAAUA,EAAQ,uBAEpB,OAAOD,CACT,CACA,iBAAiB9D,EAAS,CACxB,OAAOA,EAAQ,aAAa,2BAA2B,EAAIA,EAAQ,aAAa,2BAA2B,EAAI,kBACjH,CACA,mBAAoB,CAClB,MAAMgE,EAAc,SAAS,iBAAiB,KAAK,EAC7CC,EAA0B,IAAI,IACpC,OAAAD,EAAY,QAASE,GAAQ,CAC3B,IAAIzB,EAASyB,EAAI,cACjB,KAAOzB,GAAUA,IAAW,SAAS,MACnCwB,EAAQ,IAAIxB,CAAM,EAClBA,EAASA,EAAO,aAEpB,CAAC,EACM,MAAM,KAAKwB,CAAO,CAC3B,CACA,YAAa,CACX,OAAO,KAAK,kBACd,CACF,EACIE,EAAoB9B,EAGpB+B,EAAqB,KAAM,CAC7B,YAAY9D,EAAQC,EAAQ,CAC1B,KAAK,OAASD,EACd,KAAK,OAASC,EACd,KAAK,kBAAoB,CAAC,EAC1B,MAAM8D,GAAc,MAAM,QAAQ,KAAK,OAAO,oBAAoB,GAAK,KAAK,OAAO,qBAAqB,OAAS,EAAI,KAAK,OAAO,qBAAuB,CAAC,OAAQ,QAAS,KAAK,GAAG,IAAKC,GAAQA,EAAI,QAAQ,sBAAuB,MAAM,CAAC,EAAE,KAAK,GAAG,EACnP,KAAK,gBAAkB,IAAI,OAAO,OAAOD,CAAU,aAAc,GAAG,EACpE,KAAK,mBAAqC,IAAI,IAAI,CAEhD,OACA,OACA,OACA,OACA,QACA,QACA,SAEA,MACA,QACA,QACA,QACA,SACA,SAEA,WACA,WACA,OACA,SAEA,SACA,QACA,QAEA,MACA,SAEA,KACA,KACA,MAEA,SACA,UACA,UACA,MACA,QACA,SACA,OACA,QACA,WACA,UACA,WACA,SACA,KACA,IACA,KACF,CAAC,CACH,CAOA,oCAAoCE,EAAK,CACvC,GAAI,CAACA,EAAK,MAAO,GACjB,MAAMC,EAAyB,KAAK,OAAO,0BAA4B,CAAC,EAClEC,EAAyB,KAAK,OAAO,0BAA4B,CAAC,EAExE,MADsB,CAAC,GAAGD,EAAwB,GAAGC,CAAsB,EACtD,KAAMC,GAAcH,EAAI,SAASG,CAAS,CAAC,CAClE,CAYA,WAAWC,EAAYC,EAAM,CAC3B,MAAM/B,EAAa,KAAK,OAAO,yBACzBgC,EAAgB,IAAI,IAAIhC,CAAU,EAOxC,MANI,GAAAgC,EAAc,IAAIF,CAAU,GAG5B9B,EAAW,KAAM6B,GAAcC,EAAW,SAASD,CAAS,CAAC,GAG7D,MAAM,QAAQE,CAAI,GAAKA,EAAK,OAAS,IACnCA,EAAK,KAAML,GAAQM,EAAc,IAAIN,CAAG,CAAC,GAGzCK,EAAK,KACNL,GAAQ1B,EAAW,KAAM6B,GAAcH,EAAI,SAASG,CAAS,CAAC,CACjE,GAKJ,CAUA,iCAAiC1E,EAAS,CACxC,MAAO,CAAC,KAAK,mBAAmB,IAAIA,EAAQ,OAAO,CACrD,CAUA,iBAAiBA,EAAS,CACxB,OAAOI,EAAc,iBAAiBJ,CAAO,CAC/C,CAUA,SAASuE,EAAK,CACZ,GAAI,CACF,OAAAA,EAAMA,EAAI,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAC7B,IAAI,IAAIA,EAAK,OAAO,SAAS,IAAI,EAAE,IAC5C,MAAY,CACV,OAAOA,CACT,CACF,CAiBA,MAAM,wBAAyB,CAC7B,SAASO,EAAiCC,EAAkB,CAC1D,MAAMC,EAAY,CAAC,EACnB,SAASC,EAAwBC,EAAU,CACzC,GAAI,CAACA,EAAU,OAAO,KACtB,MAAMC,EAAWD,EAAS,MAAM,gCAAgC,EAChE,OAAOC,EAAWA,EAAS,CAAC,EAAI,IAClC,CACA,SAASC,EAAqBC,EAAiB,CAC7C,OAAKA,EACEA,EAAgB,QAAQ,iBAAkB,EAAE,EAAE,KAAK,EAD7B,EAE/B,CACA,MAAI,CAACN,GAAoB,CAAC,MAAM,QAAQA,CAAgB,GACtD,QAAQ,KACN,0EACAA,CACF,EACOC,IAELD,EAAiB,SAAW,GAGhCA,EAAiB,QAASO,GAAU,CAClC,GAAIA,GAASA,EAAM,SACjB,GAAI,CACF,UAAWC,KAAQD,EAAM,SACvB,GAAIC,EAAK,OAAS,QAAQ,eAAgB,CACxC,MAAMC,EAAkBD,EAClBZ,EAAaS,EACjBI,EAAgB,MAAM,iBAAiB,aAAa,CACtD,EACMC,EAAaD,EAAgB,MAAM,iBAAiB,aAAa,GAAK,SACtEE,EAAYF,EAAgB,MAAM,iBAAiB,YAAY,GAAK,SACpEG,EAAMH,EAAgB,MAAM,iBAAiB,KAAK,EAClDI,EAAUX,EAAwBU,CAAG,EAC3C,GAAIhB,GAAciB,EAAS,CACzB,MAAMC,EAAY,CAChB,OAAQlB,EACR,OAAQc,EACR,MAAOC,CACT,EACKV,EAAUY,CAAO,IAAGZ,EAAUY,CAAO,EAAI,CAAC,GACvBZ,EAAUY,CAAO,EAAE,KACxCE,GAAMA,EAAE,SAAWD,EAAU,QAAUC,EAAE,SAAWD,EAAU,QAAUC,EAAE,QAAUD,EAAU,KACjG,GACsBb,EAAUY,CAAO,EAAE,KAAKC,CAAS,CACzD,CACF,CAEJ,OAASE,EAAG,CACV,QAAQ,KACN,gDACAA,EACAT,CACF,CACF,MACSA,GAAS,CAACA,EAAM,UACzB,QAAQ,KACN,2EACAA,CACF,CAEJ,CAAC,EACMN,EACT,CACA,MAAMgB,EAAQ,CACZ,GAAG,SAAS,iBAAiB,wBAAwB,CACvD,EAAE,OAAQC,GAAS,CACjB,GAAI,CACF,MAAMC,EAAU,IAAI,IAAID,EAAK,IAAI,EAC3BE,EAAa,IAAI,IAAI,OAAO,SAAS,IAAI,EAC/C,OAAID,EAAQ,SAAWC,EAAW,OACzB,GAEF,CAAC,KAAK,oCAAoCF,EAAK,IAAI,CAC5D,MAAY,CACV,MAAO,EACT,CACF,CAAC,EACD,GAAID,EAAM,SAAW,EACnB,YAAK,OAAO,WAAW,yCAAyC,EACzD,CAEL,YAAa,CAAC,EAEd,UAAW,CAAC,CAEd,EAEF,MAAMI,EAAqBJ,EAAM,IAC9BK,GAAgB,MAAMA,EAAY,KAAM,CAAE,KAAM,MAAO,CAAC,EAAE,KAAMC,GAC3DA,EAAS,GACJA,EAAS,KAAK,GAEvB,QAAQ,KACN,qCAAqCD,EAAY,IAAI,KAAKC,EAAS,MAAM,IAAIA,EAAS,UAAU,EAClG,EACO,KACR,EAAE,MAAOC,IACR,QAAQ,MACN,4CAA4CF,EAAY,IAAI,IAC5DE,CACF,EACO,KACR,CACH,EACMC,EAAW,MAAM,QAAQ,IAAIJ,CAAkB,EAC/CK,EAAuB,CAAC,EAC9BD,EAAS,QAASE,GAAQ,CACxB,GAAIA,GAAOA,EAAI,KAAK,IAAM,GACxB,GAAI,CACF,MAAMpB,EAAQ,IAAI,cAClBA,EAAM,YAAYoB,CAAG,EACrBD,EAAqB,KAAKnB,CAAK,CACjC,OAASiB,EAAO,CACd,QAAQ,MACN,iDACAA,EACA;AAAA,yBACWG,EAAI,UAAU,EAAG,GAAG,CAAC,KAClC,CACF,CAEJ,CAAC,EACGD,EAAqB,OAAS,EAChC,KAAK,OAAO,WACV,YAAYA,EAAqB,MAAM,+DACzC,EAEA,KAAK,OAAO,WACV,wEACF,EAEF,MAAME,EAAqB7B,EAAiC2B,CAAoB,EAChF,MAAO,CACL,YAAaA,EACb,UAAWE,CACb,CACF,CAWA,MAAM,+BAAgC,CACpC,KAAK,OAAO,WAAW,2CAA2C,EAClE,GAAI,CACF,MAAMC,EAAS,MAAM,KAAK,uBAAuB,EACjD,KAAK,qBAAuBA,EAAO,aAAe,CAAC,EACnD,KAAK,oBAAsBA,EAAO,WAAa,CAAC,EAChD,KAAK,OAAO,WACV,uBAAuB,KAAK,qBAAqB,MAAM,6BACzD,CACF,OAASL,EAAO,CACd,KAAK,OAAO,WACV,gDACAA,CACF,EACA,KAAK,qBAAuB,CAAC,CAC/B,CACF,CAUA,uBAAwB,CACtB,OAAO,IAAI,IACT,OAAO,YAAY,iBAAiB,UAAU,EAAE,OAAQM,GAAa,KAAK,gBAAgB,KAAKA,EAAS,IAAI,CAAC,EAAE,IAAKA,GAAa,CAAC,KAAK,SAASA,EAAS,IAAI,EAAGA,EAAS,IAAI,CAAC,CAChL,CACF,CAWA,MAAM,kBAAmB,CACvB,MAAMC,EAAkB,CAAC,EACnBC,EAAgC,IAAI,IACpCC,EAAsB,CAACzB,EAAM0B,EAAW,OAAS,CACrD,MAAMtB,EAAMJ,EAAK,MAAM,iBAAiB,KAAK,EACvCZ,EAAaY,EAAK,MAAM,iBAAiB,aAAa,EAAE,QAAQ,QAAS,EAAE,EAAE,KAAK,EAClF2B,EAAS3B,EAAK,MAAM,iBAAiB,aAAa,GAAK,MACvDtF,EAAQsF,EAAK,MAAM,iBAAiB,YAAY,GAAK,SACtDuB,EAAgBnC,CAAU,IAC7BmC,EAAgBnC,CAAU,EAAI,CAAE,KAAM,CAAC,EAAG,WAA4B,IAAI,GAAM,GAOlF,MAAMwC,GAL0BjC,GAAa,CAC3C,GAAI,CAACA,EAAU,OAAO,KACtB,MAAMC,EAAWD,EAAS,MAAM,gCAAgC,EAChE,OAAOC,EAAWA,EAAS,CAAC,EAAI,IAClC,GACwCQ,CAAG,EAC3C,GAAIwB,EAAU,CACZ,IAAIC,EAASD,EACTF,IACFG,EAAS,IAAI,IAAIA,EAAQH,CAAQ,EAAE,MAErC,MAAMI,EAAa,KAAK,SAASD,CAAM,EAClCN,EAAgBnC,CAAU,EAAE,KAAK,SAAS0C,CAAU,IACvDP,EAAgBnC,CAAU,EAAE,KAAK,KAAK0C,CAAU,EAChDP,EAAgBnC,CAAU,EAAE,WAAW,IACrC,KAAK,UAAU,CAAE,OAAAuC,EAAQ,MAAAjH,CAAM,CAAC,CAClC,EAEJ,CACF,EACMqH,EAAoB,MAAO/B,GAAS,CACxC,GAAI,CACF,MAAMgC,EAAYhC,EAAK,KAIvB,GAHI,KAAK,oCAAoCgC,CAAS,GAGlDR,EAAc,IAAIQ,CAAS,EAC7B,OAEFR,EAAc,IAAIQ,CAAS,EAC3B,MAAMjB,EAAW,MAAM,MAAMiB,EAAW,CAAE,KAAM,MAAO,CAAC,EACxD,GAAI,CAACjB,EAAS,GAAI,CAChB,KAAK,OAAO,WAAW,gCAAgCA,EAAS,MAAM,EAAE,EACxE,MACF,CACA,MAAMkB,EAAU,MAAMlB,EAAS,KAAK,EAC9BmB,EAAY,IAAI,cACtBA,EAAU,YAAYD,CAAO,EAC7B,MAAM,KAAKC,EAAU,UAAY,CAAC,CAAC,EAAE,QAASC,GAAiB,CACzDA,aAAwB,iBAC1BV,EAAoBU,EAAcH,CAAS,CAE/C,CAAC,CACH,OAAShB,EAAO,CACd,KAAK,OAAO,WAAW,kCAAkCA,EAAM,OAAO,EAAE,CAC1E,CACF,EACMoB,EAAe,MAAOrC,GAAU,CACpC,GAAI,CACF,MAAMsC,EAAQ,MAAM,KAAKtC,EAAM,UAAY,CAAC,CAAC,EAC7C,UAAWC,KAAQqC,EACbrC,aAAgB,gBAClByB,EAAoBzB,EAAMD,EAAM,IAAI,EAC3BC,aAAgB,cACrBA,EAAK,WACP,MAAMoC,EAAapC,EAAK,UAAU,EAElC,MAAM+B,EAAkB/B,CAAI,EAErBA,EAAK,YACd,MAAMoC,EAAapC,EAAK,UAAU,CAGxC,OAASQ,EAAG,CACV,GAAIA,EAAE,OAAS,iBAAmBT,EAAM,KAAM,CAI5C,GAHI,KAAK,oCAAoCA,EAAM,IAAI,GAGnDyB,EAAc,IAAIzB,EAAM,IAAI,EAC9B,OAEFyB,EAAc,IAAIzB,EAAM,IAAI,EAC5B,GAAI,CACF,MAAMgB,EAAW,MAAM,MAAMhB,EAAM,KAAM,CAAE,KAAM,MAAO,CAAC,EACzD,GAAIgB,EAAS,GAAI,CACf,MAAMkB,EAAU,MAAMlB,EAAS,KAAK,EAC9BmB,EAAY,IAAI,cACtBA,EAAU,YAAYD,CAAO,EAC7B,MAAM,KAAKC,EAAU,UAAY,CAAC,CAAC,EAAE,QAASlC,GAAS,CACjDA,aAAgB,iBAClByB,EAAoBzB,EAAMD,EAAM,IAAI,CAExC,CAAC,EACD,MAAMuC,EAAc,0CACpB,IAAIC,EACJ,MAAQA,EAAcD,EAAY,KAAKL,CAAO,KAAO,MAAM,CACzD,MAAMD,EAAY,IAAI,IAAIO,EAAY,CAAC,EAAGxC,EAAM,IAAI,EAAE,KACtD,GAAI,MAAK,oCAAoCiC,CAAS,GAGlD,CAAAR,EAAc,IAAIQ,CAAS,EAG/B,CAAAR,EAAc,IAAIQ,CAAS,EAC3B,GAAI,CACF,MAAMQ,EAAiB,MAAM,MAAMR,EAAW,CAAE,KAAM,MAAO,CAAC,EAC9D,GAAIQ,EAAe,GAAI,CACrB,MAAMC,EAAgB,MAAMD,EAAe,KAAK,EAC1CE,EAAkB,IAAI,cAC5BA,EAAgB,YAAYD,CAAa,EACzC,MAAM,KAAKC,EAAgB,UAAY,CAAC,CAAC,EAAE,QAASP,GAAiB,CAC/DA,aAAwB,iBAC1BV,EAAoBU,EAAcH,CAAS,CAE/C,CAAC,CACH,CACF,OAASW,EAAa,CACpB,KAAK,OAAO,WAAW,0BAA0BX,CAAS,KAAKW,EAAY,OAAO,EAAE,CACtF,EACF,CACF,CACF,OAASC,EAAY,CACnB,KAAK,OAAO,WAAW,6BAA6B7C,EAAM,IAAI,KAAK6C,EAAW,OAAO,EAAE,CACzF,CACF,MACE,KAAK,OAAO,WAAW,gCAAgCpC,EAAE,OAAO,EAAE,CAEtE,CACF,EACMqC,EAAS,MAAM,KAAK,SAAS,WAAW,EAC9C,UAAW9C,KAAS8C,EAClB,MAAMT,EAAarC,CAAK,EAE1B,MAAM+C,EAAsB,SAAS,iBAAiB,OAAO,EAC7D,UAAWC,KAAgBD,EAAqB,CAC9C,MAAMb,EAAUc,EAAa,aAAeA,EAAa,WAAa,GAChET,EAAc,sDACpB,IAAIC,EACJ,MAAQA,EAAcD,EAAY,KAAKL,CAAO,KAAO,MAAM,CACzD,MAAMD,EAAYO,EAAY,CAAC,EAC/B,GAAI,MAAK,oCAAoCP,CAAS,GAGlD,CAAAR,EAAc,IAAIQ,CAAS,EAG/B,CAAAR,EAAc,IAAIQ,CAAS,EAC3B,GAAI,CACF,MAAMjB,EAAW,MAAM,MAAMiB,EAAW,CAAE,KAAM,MAAO,CAAC,EACxD,GAAIjB,EAAS,GAAI,CACf,MAAM0B,EAAgB,MAAM1B,EAAS,KAAK,EACpCmB,EAAY,IAAI,cACtBA,EAAU,YAAYO,CAAa,EACnC,MAAM,KAAKP,EAAU,UAAY,CAAC,CAAC,EAAE,QAASC,GAAiB,CACzDA,aAAwB,iBAC1BV,EAAoBU,EAAcH,CAAS,CAE/C,CAAC,CACH,CACF,OAASW,EAAa,CACpB,KAAK,OAAO,WAAW,iCAAiCX,CAAS,KAAKW,EAAY,OAAO,EAAE,CAC7F,EACF,CACF,CACA,cAAO,OAAOpB,CAAe,EAAE,QAASyB,GAAa,CACnDA,EAAS,WAAa,MAAM,KAAKA,EAAS,UAAU,EAAE,IAAKzC,GAAM,KAAK,MAAMA,CAAC,CAAC,CAChF,CAAC,EACMgB,CACT,CAOA,mBAAmB9G,EAAS,CAC1B,GAAI,CAAC,KAAK,iBAAiBA,CAAO,EAAG,MAAO,GAC5C,MAAMD,EAAOC,EAAQ,sBAAsB,EACrC0C,EAAY,OAAO,aAAe,SAAS,gBAAgB,UAC3D8F,EAAazI,EAAK,IAAM2C,EACxB+F,EAAe,OAAO,aAAe,SAAS,gBAAgB,aACpE,OAAOD,GAAcC,CACvB,CAWA,sBAAsBzI,EAAS,CAC7B,OAAO,KAAK,iCAAiCA,CAAO,GAAK,KAAK,mBAAmBA,CAAO,CAC1F,CASA,MAAM,KAAM,CACV,MAAM,SAAS,MAAM,MACrB,MAAM,KAAK,8BAA8B,EACzC,MAAM0I,EAAqB,KAAK,sBAAsB,EAChD5B,EAAkB,MAAM,KAAK,iBAAiB,EAC9C6B,EAA8B,IAAI,IAClCC,EAAuB,MAAM,KAAK,qBAAqB,KAAK,mBAAmB,EACpE,MAAM,KAAK,SAAS,qBAAqB,GAAG,CAAC,EAAE,OAAQC,GAAO,KAAK,sBAAsBA,CAAE,CAAC,EACpG,QAAS7I,GAAY,CAC5B,MAAM8I,EAAqB,CAAC7I,EAAO8I,EAAgB,OAAS,CAC1D,GAAI,CAAC9I,GAAS,CAAC,KAAK,iBAAiBD,CAAO,EAAG,OAC/C,MAAM2E,EAAa1E,EAAM,WAAW,MAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,SAAU,EAAE,EAAE,KAAK,EAE7E,IADmB8I,EAAgB9I,EAAM,UAAY,QAAUA,EAAM,UAAY,KAAOD,EAAQ,YAAY,KAAK,IAC/F8G,EAAgBnC,CAAU,EAAG,CAC7C,IAAIC,EAAOkC,EAAgBnC,CAAU,EAAE,KACnC,CAAC,KAAK,WAAWA,EAAYC,CAAI,GAAK,CAAC+D,EAAY,IAAIhE,CAAU,IACnEgE,EAAY,IAAIhE,EAAY,CAC1B,SAA0B,IAAI,IAC9B,KAAAC,EACA,WAAYkC,EAAgBnC,CAAU,EAAE,UAC1C,CAAC,EACDgE,EAAY,IAAIhE,CAAU,EAAE,SAAS,IAAI3E,CAAO,EAEpD,CACF,EACA,GAAI,CACF8I,EAAmB,OAAO,iBAAiB9I,CAAO,CAAC,EACnD,CAAC,WAAY,SAAS,EAAE,QAASgJ,GAAW,CAC1CF,EAAmB,OAAO,iBAAiB9I,EAASgJ,CAAM,EAAGA,CAAM,CACrE,CAAC,CACH,OAASjD,EAAG,CACV,KAAK,OAAO,WAAW,4BAA6BA,CAAC,CACvD,CACF,CAAC,EACD,MAAMkD,EAAoB,KAAK,iBAAiBL,EAAsBD,EAAaD,CAAkB,EACrG,GAAI,CAAC,OAAO,KAAKO,EAAkB,QAAQ,EAAE,QAAU,CAAC,OAAO,KAAKA,EAAkB,aAAa,EAAE,QAAU,CAAC,OAAO,KAAKA,EAAkB,WAAW,EAAE,OAAQ,CACjK,KAAK,OAAO,WAAW,gCAAgC,EACvD,MACF,CACA,KAAK,OAAO,WAAW,wBAAyBA,CAAiB,EACjE,KAAK,kBAAoB,CAAC,GAAG,IAAI,IAAI,OAAO,OAAOA,EAAkB,QAAQ,EAAE,QAASC,GAASA,EAAK,WAAW,IAAKrD,GAAcA,EAAU,GAAG,CAAC,CAAC,CAAC,CACtJ,CAUA,iBAAiB+C,EAAsBD,EAAaD,EAAoB,CACtE,MAAMS,EAAW,CAAC,EACZC,EAAqB,CAAC,EAC5B,OAAIT,EAAY,KAAO,GACrBA,EAAY,QAAQ,CAACU,EAAM1E,IAAe,CACxC,GAAI0E,EAAK,WAAY,CACnB,MAAMvH,EAAW,MAAM,KAAKuH,EAAK,QAAQ,EACnCC,EAAgBxH,EAAS,OAAQ+G,GAAO,KAAK,mBAAmBA,CAAE,CAAC,EACnEU,EAAgBzH,EAAS,OAAQ+G,GAAO,CAAC,KAAK,mBAAmBA,CAAE,CAAC,EAC1EQ,EAAK,WAAW,QAASxD,GAAc,CACrC,IAAI2D,EAAc,KAClB,UAAWC,KAAYJ,EAAK,KAAM,CAChC,MAAMK,EAAqB,KAAK,SAASD,CAAQ,EACjD,GAAIf,EAAmB,IAAIgB,CAAkB,EAAG,CAC9CF,EAAcd,EAAmB,IAAIgB,CAAkB,EACvD,KACF,CACF,CACIF,IACGL,EAASxE,CAAU,IACtBwE,EAASxE,CAAU,EAAI,CACrB,KAAM,SACN,WAAY,CAAC,EACb,aAAc,CACZ,UAAW2E,EAAc,OACzB,UAAWC,EAAc,OACzB,MAAOzH,EAAS,MAClB,EACA,SAAU,CACR,UAA2B,IAAI,IAC/B,UAA2B,IAAI,GACjC,CACF,GAEFqH,EAASxE,CAAU,EAAE,WAAW,KAAK,CACnC,OAAQkB,EAAU,OAClB,MAAOA,EAAU,MACjB,IAAK2D,EACL,aAAc,CACZ,UAAWF,EAAc,OACzB,UAAWC,EAAc,OACzB,MAAOzH,EAAS,MAClB,CACF,CAAC,EACGwH,EAAc,OAAS,GACzBH,EAASxE,CAAU,EAAE,SAAS,UAAU,IAAI6E,CAAW,EAErDD,EAAc,OAAS,GACzBJ,EAASxE,CAAU,EAAE,SAAS,UAAU,IAAI6E,CAAW,EAG7D,CAAC,EACGL,EAASxE,CAAU,IACrByE,EAAmBzE,CAAU,EAAI,CAC/B,WAAYwE,EAASxE,CAAU,EAAE,WACjC,aAAc,CAAE,GAAGwE,EAASxE,CAAU,EAAE,YAAa,EACrD,SAAU,CAAE,GAAGwE,EAASxE,CAAU,EAAE,QAAS,CAC/C,EAEJ,CACF,CAAC,EAEC,OAAO,KAAKiE,CAAoB,EAAE,OAAS,GAC7C,OAAO,QAAQA,CAAoB,EAAE,QAAQ,CAAC,CAACrE,EAAK8E,CAAI,IAAM,CAC5D,MAAMC,EAAgB,MAAM,KAAKD,EAAK,QAAQ,EAAE,OAAQR,GAAO,KAAK,mBAAmBA,CAAE,CAAC,EACpFU,EAAgB,MAAM,KAAKF,EAAK,QAAQ,EAAE,OAAQR,GAAO,CAAC,KAAK,mBAAmBA,CAAE,CAAC,GACvFQ,EAAK,aAAa,UAAY,GAAKC,EAAc,OAAS,IAC5DD,EAAK,WAAW,QAASxD,GAAc,CAChCsD,EAAStD,EAAU,MAAM,IAC5BsD,EAAStD,EAAU,MAAM,EAAI,CAC3B,KAAM,WACN,WAAY,CAAC,EAEb,aAAc,CACZ,UAAW,EACX,UAAW,EACX,MAAO,CACT,EAEA,SAAU,CACR,UAA2B,IAAI,IAC/B,UAA2B,IAAI,GACjC,CACF,GAEFsD,EAAStD,EAAU,MAAM,EAAE,WAAW,KAAK,CACzC,OAAQA,EAAU,OAClB,MAAOA,EAAU,MACjB,IAAAtB,EACA,aAAc,CACZ,UAAW+E,EAAc,OACzB,UAAWC,EAAc,OACzB,MAAOF,EAAK,SAAS,MACvB,CACF,CAAC,EACDF,EAAStD,EAAU,MAAM,EAAE,aAAa,WAAayD,EAAc,OACnEH,EAAStD,EAAU,MAAM,EAAE,aAAa,WAAa0D,EAAc,OACnEJ,EAAStD,EAAU,MAAM,EAAE,aAAa,OAASwD,EAAK,SAAS,OAC3DC,EAAc,OAAS,GACzBH,EAAStD,EAAU,MAAM,EAAE,SAAS,UAAU,IAAItB,CAAG,EAEnDgF,EAAc,OAAS,GACzBJ,EAAStD,EAAU,MAAM,EAAE,SAAS,UAAU,IAAItB,CAAG,CAEzD,CAAC,CAEL,CAAC,EAEH,OAAO,OAAO4E,CAAQ,EAAE,QAASD,GAAS,CACxCA,EAAK,SAAW,CACd,UAAWA,EAAK,SAAS,UAAU,KACnC,UAAWA,EAAK,SAAS,UAAU,KACnC,MAAwB,IAAI,IAAI,CAAC,GAAGA,EAAK,SAAS,UAAW,GAAGA,EAAK,SAAS,SAAS,CAAC,EAAG,IAC7F,CACF,CAAC,EACD,OAAO,OAAOE,CAAkB,EAAE,QAASF,GAAS,CAC9CA,EAAK,SAAS,qBAAqB,MACrCA,EAAK,SAAW,CACd,UAAWA,EAAK,SAAS,UAAU,KACnC,UAAWA,EAAK,SAAS,UAAU,KACnC,MAAwB,IAAI,IAAI,CAAC,GAAGA,EAAK,SAAS,UAAW,GAAGA,EAAK,SAAS,SAAS,CAAC,EAAG,IAC7F,EAEJ,CAAC,EACM,CACL,cAAe,OAAO,YACpB,OAAO,QAAQN,CAAoB,EAAE,OAClCe,GAAUA,EAAM,CAAC,EAAE,aAAa,UAAY,CAC/C,CACF,EACA,YAAaP,EACb,SAAAD,CACF,CACF,CAWA,MAAM,qBAAqBnE,EAAW,CACpC,MAAMpD,EAA0B,IAAI,IAC9BE,EAAW,MAAM,KAAK,SAAS,qBAAqB,GAAG,CAAC,EAAE,OAAQ+G,GAAO,KAAK,sBAAsBA,CAAE,CAAC,EACvGe,EAA0B,IAAI,IACpC,OAAO,QAAQ5E,CAAS,EAAE,QAAQ,CAAC,CAACT,EAAKsF,CAAU,IAAM,CACvDA,EAAW,QAAShE,GAAc,CAChC,MAAMiE,EAAM,GAAGjE,EAAU,MAAM,IAAIA,EAAU,MAAM,IAAIA,EAAU,KAAK,GACtE+D,EAAQ,IAAIE,EAAK,CAAE,IAAAvF,EAAK,GAAGsB,CAAU,CAAC,CACxC,CAAC,CACH,CAAC,EACD,MAAMkE,EAAyB9J,GAAU,CACvC,MAAM+J,EAAS/J,EAAM,WAAW,MAAM,GAAG,EAAE,CAAC,EAAE,QAAQ,SAAU,EAAE,EAAE,KAAK,EACnEiH,EAASjH,EAAM,WACfyF,EAAYzF,EAAM,UAClB6J,EAAM,GAAGE,CAAM,IAAI9C,CAAM,IAAIxB,CAAS,GAC5C,IAAIuE,EAAWL,EAAQ,IAAIE,CAAG,EAC9B,GAAI,CAACG,GAAY/C,IAAW,MAAO,CACjC,MAAMgD,EAAc,GAAGF,CAAM,QAAQtE,CAAS,GAC9CuE,EAAWL,EAAQ,IAAIM,CAAW,CACpC,CACA,OAAOD,CACT,EACA,OAAAnI,EAAS,QAAS9B,GAAY,CAC5B,GAAIA,EAAQ,YAAY,KAAK,EAAG,CAC9B,MAAMC,EAAQ,OAAO,iBAAiBD,CAAO,EACvCiK,EAAWF,EAAsB9J,CAAK,EACxCgK,GACE,CAAC,KAAK,WAAWA,EAAS,OAAQ,CAACA,EAAS,GAAG,CAAC,GAAK,CAACrI,EAAQ,IAAIqI,EAAS,GAAG,IAChFrI,EAAQ,IAAIqI,EAAS,IAAK,CACxB,SAA0B,IAAI,IAC9B,WAA4B,IAAI,GAClC,CAAC,EACDrI,EAAQ,IAAIqI,EAAS,GAAG,EAAE,SAAS,IAAIjK,CAAO,EAC9C4B,EAAQ,IAAIqI,EAAS,GAAG,EAAE,WAAW,IAAI,KAAK,UAAU,CACtD,OAAQA,EAAS,OACjB,OAAQA,EAAS,OACjB,MAAOA,EAAS,KAClB,CAAC,CAAC,EAGR,CACA,CAAC,WAAY,SAAS,EAAE,QAASjB,GAAW,CAC1C,MAAMmB,EAAc,OAAO,iBAAiBnK,EAASgJ,CAAM,EAC3D,GAAImB,EAAY,UAAY,QAAUA,EAAY,UAAY,KAAM,CAClE,MAAMF,EAAWF,EAAsBI,CAAW,EAC9CF,GACE,CAAC,KAAK,WAAWA,EAAS,OAAQ,CAACA,EAAS,GAAG,CAAC,GAAK,CAACrI,EAAQ,IAAIqI,EAAS,GAAG,IAChFrI,EAAQ,IAAIqI,EAAS,IAAK,CACxB,SAA0B,IAAI,IAC9B,WAA4B,IAAI,GAClC,CAAC,EACDrI,EAAQ,IAAIqI,EAAS,GAAG,EAAE,SAAS,IAAIjK,CAAO,EAC9C4B,EAAQ,IAAIqI,EAAS,GAAG,EAAE,WAAW,IAAI,KAAK,UAAU,CACtD,OAAQA,EAAS,OACjB,OAAQA,EAAS,OACjB,MAAOA,EAAS,KAClB,CAAC,CAAC,EAGR,CACF,CAAC,CACH,CAAC,EACM,OAAO,YACZ,MAAM,KAAKrI,EAAQ,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC2C,EAAK8E,CAAI,IAAM,CACjD9E,EACA,CACE,aAAc,CACZ,UAAW,MAAM,KAAK8E,EAAK,QAAQ,EAAE,OAAQR,GAAO,KAAK,mBAAmBA,CAAE,CAAC,EAAE,OACjF,MAAOQ,EAAK,SAAS,IACvB,EACA,WAAY,MAAM,KAAKA,EAAK,UAAU,EAAE,IAAKvD,GAAM,KAAK,MAAMA,CAAC,CAAC,EAChE,SAAU,MAAM,KAAKuD,EAAK,QAAQ,CACpC,CACF,CAAC,CACH,CACF,CAOA,YAAa,CACX,OAAO,KAAK,iBACd,CACF,EACIe,EAA6BhG,EAG7BiG,EAAiC,KAAM,CACzC,YAAY/J,EAAQC,EAAQ,CAC1B,KAAK,OAASA,EACd,KAAK,OAAS,CAAC,EACf,KAAK,iBAAmBD,EAAO,sCAC/B,KAAK,iBAAmBA,EAAO,oCAC/B,KAAK,aAA+B,IAAI,IACxC,KAAK,cAAgC,IAAI,GAC3C,CAMA,MAAM,KAAM,CACO,SAAS,iBACxB,GAAG,KAAK,iBAAiB,KAAK,IAAI,CAAC,UAAU,KAAK,iBAAiB,KAAK,IAAI,CAAC,WAAW,KAAK,iBAAiB,KAAK,IAAI,CAAC,UAAU,KAAK,iBAAiB,KAAK,IAAI,CAAC,QACpK,EACS,QAASuI,GAAO,KAAK,eAAeA,CAAE,CAAC,EAChD,KAAK,OAAO,WAAW,CAAE,aAAc,KAAK,gBAAgB,EAAG,cAAe,MAAM,KAAK,KAAK,aAAa,CAAE,CAAC,CAChH,CASA,eAAeA,EAAI,CACjB,GAAI,CACF,MAAMtE,EAAM,IAAI,IAAIsE,EAAG,KAAOA,EAAG,MAAQ,GAAI,SAAS,IAAI,EAC1D,GAAI,KAAK,WAAWA,CAAE,EAAG,CACvB,KAAK,cAAc,IAAI,KAAK,sBAAsBtE,EAAKsE,CAAE,CAAC,EAC1D,MACF,CACI,KAAK,iBAAiBtE,CAAG,IAC3B,KAAK,aAAa,IAAI,GAAGA,EAAI,QAAQ,IAAIsE,EAAG,QAAQ,YAAY,CAAC,EAAE,EACnE,KAAK,OAAS,CAAC,GAAG,IAAI,IAAI,KAAK,OAAO,OAAOtE,EAAI,MAAM,CAAC,CAAC,EAE7D,OAASwB,EAAG,CACV,KAAK,OAAO,WAAWA,CAAC,CAC1B,CACF,CAUA,WAAW8C,EAAI,CACb,MAAMyB,EAAYzB,EAAG,UAAU,UAAU,EAAGA,EAAG,UAAU,QAAQ,GAAG,EAAI,CAAC,EACzE,OAAO,KAAK,iBAAiB,KAC1B7F,GAAYsH,EAAU,SAAStH,CAAO,CACzC,CACF,CAUA,mBAAmBuB,EAAK,CACtB,OAAO,KAAK,iBAAiB,KAC1BvB,GAAYA,EAAQ,OAAS,UAAYuB,EAAI,SAAS,SAASvB,EAAQ,KAAK,CAC/E,CACF,CAUA,iBAAiBuB,EAAK,CACpB,OAAOA,EAAI,WAAa,SAAS,UAAYA,EAAI,QACnD,CAQA,sBAAsBA,EAAKsE,EAAI,CAC7B,MAAO,CAAE,OAAQtE,EAAI,SAAU,YAAasE,EAAG,QAAQ,YAAY,CAAE,CACvE,CASA,iBAAkB,CAChB,OAAO,MAAM,KAAK,KAAK,YAAY,EAAE,IAAKhI,GAAS,CACjD,MAAM0J,EAAkB1J,EAAK,YAAY,GAAG,EAC5C,MAAO,CACLA,EAAK,UAAU,EAAG0J,CAAe,EAEjC1J,EAAK,UAAU0J,EAAkB,CAAC,CAEpC,CACF,CAAC,CACH,CAQA,YAAa,CACX,OAAO,KAAK,MACd,CACF,EACIC,EAAyCH,EAGzCI,EAAS,KAAM,CACjB,YAAYC,EAAS,CACnB,KAAK,QAAUA,CACjB,CACA,WAAWC,EAAOC,EAAM,GAAI,CAC1B,GAAK,KAAK,QAGV,IAAIA,IAAQ,GAAI,CACd,QAAQ,IAAID,EAAOC,CAAG,EACtB,MACF,CACA,QAAQ,IAAID,CAAK,EACnB,CACA,kBAAkBC,EAAK1K,EAAQ,QAAS,CACjC,KAAK,SAGV,QAAQ,IAAI,KAAK0K,CAAG,GAAI,UAAU1K,CAAK,GAAG,CAC5C,CACF,EACI2K,EAAiBJ,EAGjBK,EAAgB,KAAM,CACxB,YAAYxK,EAAQ,CAClB,KAAK,OAASA,EACd,KAAK,UAAY,KACjB,KAAK,UAAY,KACjB,KAAK,mBAAqB,KAC1B,KAAK,+BAAiC,KACtC,KAAK,eAAiB,KACtB,KAAK,UAAY,GACjB,KAAK,OAAS,IAAIuK,EAAe,KAAK,OAAO,KAAK,CACpD,CACA,MAAM,MAAO,CAEX,GADA,KAAK,YAA8B,IAAI,KACnC,CAAC,MAAM,KAAK,sBAAsB,EAAG,CACvC,KAAK,UAAU,EACf,MACF,CACA,GAAIzK,EAAc,eAAe,EAAG,CAClC,KAAK,OAAO,WAAW,gDAAgD,EACvE,KAAK,UAAU,EACf,MACF,CACA,KAAK,eAAiB,WAAW,IAAM,CACrC,KAAK,oBAAoB,CAC3B,EAAG,GAAG,EACN,MAAM2K,EAAoB,MAAM,KAAK,oBAAoB,EACnDC,EAAoB,KAAK,OAAO,OAAO,MAAQD,IAAsB,IAASA,EAAkB,MAAQ,IACxGE,EAAoB,KAAK,OAAO,OAAO,MAAQF,IAAsB,IAASA,EAAkB,MAAQ,IACxGG,EAA6B,KAAK,OAAO,OAAO,gBAAkBH,IAAsB,IAASA,EAAkB,gBAAkB,IACrII,EAAyC,KAAK,OAAO,OAAO,6BAA+BJ,IAAsB,IAASA,EAAkB,6BAA+B,IAC7KC,GACF,KAAK,UAAY,IAAI5I,EAAkB,KAAK,OAAQ,KAAK,MAAM,EAC/D,MAAM,KAAK,UAAU,IAAI,GAEzB,KAAK,OAAO,WAAW,gFAAgF,EAErG6I,GACF,KAAK,UAAY,IAAI9G,EAAkB,KAAK,OAAQ,KAAK,MAAM,EAC/D,MAAM,KAAK,UAAU,IAAI,GAEzB,KAAK,OAAO,WAAW,gFAAgF,EAErG+G,GACF,KAAK,mBAAqB,IAAId,EAA2B,KAAK,OAAQ,KAAK,MAAM,EACjF,MAAM,KAAK,mBAAmB,IAAI,GAElC,KAAK,OAAO,WAAW,yFAAyF,EAE9Ge,GACF,KAAK,+BAAiC,IAAIX,EAAuC,KAAK,OAAQ,KAAK,MAAM,EACzG,MAAM,KAAK,+BAA+B,IAAI,GAE9C,KAAK,OAAO,WAAW,qGAAqG,EAE1HQ,GAAqBC,GAAqBC,GAA8BC,EAC1E,KAAK,uBAAuB,GAE5B,KAAK,OAAO,WAAW,uDAAuD,EAC9E,KAAK,UAAU,EAEnB,CACA,MAAM,uBAAwB,CAC5B,MAAM1L,EAAY,CAChB,MAAO,KAAK,OAAO,gBACnB,OAAQ,KAAK,OAAO,gBACtB,EACA,OAAIW,EAAc,qBAAqB,KAAK,OAAO,UAAWX,CAAS,GACrE,KAAK,OAAO,WAAW,mDAAmD,EACnE,IAEF,EACT,CACA,MAAM,qBAAsB,CAC1B,GAAI,CAACW,EAAc,aAAa,EAC9B,MAAO,GAET,IAAIgL,EAAa,IAAI,SACrB,OAAAA,EAAW,OAAO,SAAU,qBAAqB,EACjDA,EAAW,OAAO,sBAAuB,KAAK,OAAO,KAAK,EAC1DA,EAAW,OAAO,MAAO,KAAK,OAAO,GAAG,EACxCA,EAAW,OAAO,YAAa,KAAK,OAAO,SAAS,GACvB,MAAM,MAAM,KAAK,OAAO,SAAU,CAC7D,OAAQ,OACR,YAAa,cACb,KAAMA,CACR,CAAC,EAAE,KAAM/B,GAASA,EAAK,KAAK,CAAC,GACD,IAC9B,CACA,wBAAyB,CACvB,MAAMgC,EAAU,CACd,IAAK,KAAK,UAAY,KAAK,UAAU,WAAW,EAAI,KACpD,IAAK,KAAK,UAAY,KAAK,UAAU,WAAW,EAAI,KACpD,cAAe,KAAK,mBAAqB,KAAK,mBAAmB,WAAW,EAAI,KAChF,2BAA4B,KAAK,+BAAiC,KAAK,+BAA+B,WAAW,EAAI,IACvH,EACMhC,EAAO,IAAI,SACjBA,EAAK,OAAO,SAAU,eAAe,EACrCA,EAAK,OAAO,sBAAuB,KAAK,OAAO,KAAK,EACpDA,EAAK,OAAO,MAAO,KAAK,OAAO,GAAG,EAClCA,EAAK,OAAO,YAAa,KAAK,OAAO,SAAS,EAC9CA,EAAK,OAAO,SAAU,KAAK,gBAAgB,CAAC,EAC5CA,EAAK,OAAO,UAAW,KAAK,UAAUgC,CAAO,CAAC,EAC9C,MAAM,KAAK,OAAO,SAAU,CAC1B,OAAQ,OACR,YAAa,cACb,KAAMhC,EACN,QAAS,CACP,wBAAyB,EAC3B,CACF,CAAC,EAAE,KAAM/C,GAAaA,EAAS,KAAK,CAAC,EAAE,KAAMgF,GAAU,CACrD,KAAK,OAAO,WAAWA,EAAM,KAAK,GAAG,CACvC,CAAC,EAAE,MAAO/E,GAAU,CAClB,KAAK,OAAO,WAAWA,CAAK,CAC9B,CAAC,EAAE,QAAQ,IAAM,CACf,KAAK,UAAU,CACjB,CAAC,CACH,CACA,iBAAkB,CAChB,OAAW,KAAK,YAAZ,GACK,KAAK,UAGV,KADgC,IAAI,KAAS,KAAK,aAAe,IAE5D,UAEF,SACT,CACA,qBAAsB,CACpB,KAAK,uBAAuB,CAC9B,CACA,WAAY,CACW,SAAS,cAAc,8BAA8B,EAC7D,aAAa,mBAAoB,MAAM,EACpD,aAAa,KAAK,cAAc,CAClC,CACF,EACIgF,EAAwBT,GAG1BU,GAAuB,CACvB,GAAI,CAACA,EACH,OAEF,MAAMC,EAAW,IAAIF,EAAsBC,CAAkB,EAC7D,GAAI,SAAS,aAAe,UAAW,CACrC,WAAW,IAAM,CACfC,EAAS,KAAK,CAChB,EAAGD,EAAmB,KAAK,EAC3B,MACF,CACA,SAAS,iBAAiB,mBAAoB,IAAM,CAClD,WAAW,IAAM,CACfC,EAAS,KAAK,CAChB,EAAGD,EAAmB,KAAK,CAC7B,CAAC,CACH,GAAG,OAAO,kBAAkB,EAC5B,IAAIE,EAA2BH,CACjC,GAAG", "names": ["BeaconUtils", "is_mobile", "threshold", "screenWidth", "screenHeight", "isNotValidForMobile", "isNotValidForDesktop", "signature", "rect", "element", "style", "color", "filter", "Utils_default", "BeaconLcp", "config", "logger", "above_the_fold_images", "err", "count", "lcpElements", "imgElement", "item", "a", "b", "candidate", "visibleWidth", "visibleHeight", "nodeName", "element_info", "css_bg_url_rgx", "source", "imageElement", "img", "bg_props", "prop", "full_bg_prop", "matches", "m", "elements", "firstElementWithInfo", "elementInfo", "image", "isImageOrVideo", "isBgImageOrPicture", "BeaconLcp_default", "BeaconLrc", "elementsInView", "svgUseTargets", "depth", "parent", "scrollTop", "skipStrings", "str", "exclusions", "i", "attribute", "pattern", "attributeValue", "conflictingElements", "computedStyle", "<PERSON><PERSON><PERSON><PERSON>", "margin", "child", "childStyle", "childN<PERSON><PERSON><PERSON><PERSON><PERSON>", "distance", "hash", "conflicts", "can_push_hash", "position", "pos", "sibling", "useElements", "targets", "use", "BeaconLrc_default", "BeaconPreloadFonts", "extensions", "ext", "url", "externalFontExclusions", "preloadFontsExclusions", "exclusion", "fontFamily", "urls", "exclusionsSet", "generateFontPairsFromStyleSheets", "styleSheetsArray", "fontPairs", "_extractFirstUrlFromSrc", "srcValue", "urlMatch", "_cleanFontFamilyName", "fontFamilyValue", "sheet", "rule", "cssFontFaceRule", "fontWeight", "fontStyle", "src", "fontUrl", "variation", "v", "e", "links", "link", "linkUrl", "currentUrl", "fetchedCssPromises", "linkElement", "response", "error", "cssTexts", "temporaryStyleSheets", "txt", "processedFontPairs", "result", "resource", "stylesheetFonts", "processedUrls", "processFontFaceRule", "baseHref", "weight", "firstUrl", "rawUrl", "normalized", "processImportRule", "importUrl", "cssText", "tempSheet", "importedRule", "processSheet", "rules", "importRegex", "importMatch", "importResponse", "importCssText", "tempImportSheet", "importError", "fetchError", "sheets", "inlineStyleElements", "styleElement", "fontData", "elementTop", "foldPosition", "networkLoadedFonts", "hostedFonts", "externalFontsResults", "el", "processElementFont", "pseudoElement", "pseudo", "aboveTheFoldFonts", "font", "allFonts", "hostedFontsResults", "data", "aboveElements", "belowElements", "matchingUrl", "styleUrl", "normalizedStyleUrl", "entry", "fontMap", "variations", "key", "getFontInfoForElement", "family", "fontInfo", "fallback<PERSON><PERSON>", "pseudoStyle", "BeaconPreloadFonts_default", "BeaconPreconnectExternalDomain", "outerHTML", "lastHyphenIndex", "BeaconPreconnectExternalDomain_default", "<PERSON><PERSON>", "enabled", "label", "msg", "Logger_default", "BeaconManager", "isGeneratedBefore", "shouldGenerateLcp", "shouldGeneratelrc", "shouldGeneratePreloadFonts", "shouldGeneratePreconnectExternalDomain", "data_check", "results", "data2", "BeaconManager_default", "rocket_beacon_data", "instance", "BeaconEntryPoint_default"]}