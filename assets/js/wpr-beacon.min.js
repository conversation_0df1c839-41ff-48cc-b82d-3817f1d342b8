(()=>{var _=class{static getScreenWidth(){return window.innerWidth||document.documentElement.clientWidth}static getScreenHeight(){return window.innerHeight||document.documentElement.clientHeight}static isNotValidScreensize(e,t){const s=this.getScreenWidth(),l=this.getScreenHeight(),r=e&&(s>t.width||l>t.height),i=!e&&(s<t.width||l<t.height);return r||i}static isPageCached(){const e=document.documentElement.nextSibling&&document.documentElement.nextSibling.data?document.documentElement.nextSibling.data:"";return e&&e.includes("Debug: cached")}static isIntersecting(e){return e.bottom>=0&&e.right>=0&&e.top<=(window.innerHeight||document.documentElement.clientHeight)&&e.left<=(window.innerWidth||document.documentElement.clientWidth)}static isPageScrolled(){return window.pageYOffset>0||document.documentElement.scrollTop>0}static isElementVisible(e){const t=window.getComputedStyle(e),s=e.getBoundingClientRect();return!t||this.hasTransparentText(e)?!1:!(t.display==="none"||t.visibility==="hidden"||t.opacity==="0"||s.width===0||s.height===0)}static hasTransparentText(e){const t=window.getComputedStyle(e);if(!t)return!1;const s=t.color||"",l=t.filter||"";return!!(s==="transparent"||s.match(/rgba\(\d+,\s*\d+,\s*\d+,\s*0\)/)||s.match(/hsla\(\d+,\s*\d+%,\s*\d+%,\s*0\)/)||s.match(/#[0-9a-fA-F]{6}00/)||l.includes("opacity(0)"))}},E=_,F=class{constructor(e,t){this.config=e,this.performanceImages=[],this.logger=t}async run(){try{const e=this._generateLcpCandidates(1/0);e&&(this._initWithFirstElementWithInfo(e),this._fillATFWithoutDuplications(e))}catch(e){this.errorCode="script_error",this.logger.logMessage("Script Error: "+e)}}_generateLcpCandidates(e){const t=document.querySelectorAll(this.config.elements);return t.length<=0?[]:Array.from(t).map(r=>{if(r.nodeName.toLowerCase()==="img"&&r.parentElement.nodeName.toLowerCase()==="picture")return null;let i;if(r.nodeName.toLowerCase()==="picture"){const o=r.querySelector("img");if(o)i=o.getBoundingClientRect();else return null}else i=r.getBoundingClientRect();return{element:r,rect:i}}).filter(r=>r!==null).filter(r=>r.rect.width>0&&r.rect.height>0&&E.isIntersecting(r.rect)&&E.isElementVisible(r.element)).map(r=>({item:r,area:this._getElementArea(r.rect),elementInfo:this._getElementInfo(r.element)})).sort((r,i)=>i.area-r.area).slice(0,e).map(r=>({element:r.item.element,elementInfo:r.elementInfo}))}_getElementArea(e){const t=Math.min(e.width,(window.innerWidth||document.documentElement.clientWidth)-e.left),s=Math.min(e.height,(window.innerHeight||document.documentElement.clientHeight)-e.top);return t*s}_getElementInfo(e){const t=e.nodeName.toLowerCase(),s={type:"",src:"",srcset:"",sizes:"",sources:[],bg_set:[],current_src:""},l=/url\(\s*?['"]?\s*?(.+?)\s*?["']?\s*?\)/ig;if(t==="img"&&e.srcset)s.type="img-srcset",s.src=e.src,s.srcset=e.srcset,s.sizes=e.sizes,s.current_src=e.currentSrc;else if(t==="img")s.type="img",s.src=e.src,s.current_src=e.currentSrc;else if(t==="video"){s.type="img";const r=e.querySelector("source");s.src=e.poster||(r?r.src:""),s.current_src=s.src}else if(t==="svg"){const r=e.querySelector("image");r&&(s.type="img",s.src=r.getAttribute("href")||"",s.current_src=s.src)}else if(t==="picture"){s.type="picture";const r=e.querySelector("img");s.src=r?r.src:"",s.sources=Array.from(e.querySelectorAll("source")).map(i=>({srcset:i.srcset||"",media:i.media||"",type:i.type||"",sizes:i.sizes||""}))}else{const i=[window.getComputedStyle(e,null).getPropertyValue("background-image"),getComputedStyle(e,":after").getPropertyValue("background-image"),getComputedStyle(e,":before").getPropertyValue("background-image")].filter(a=>a!=="none");if(i.length===0)return null;const o=i[0];if(s.type="bg-img",o.includes("image-set(")&&(s.type="bg-img-set"),!o||o===""||o.includes("data:image"))return null;const n=[...o.matchAll(l)];if(s.bg_set=n.map(a=>a[1]?{src:a[1].trim()+(a[2]?" "+a[2].trim():"")}:{}),s.bg_set.every(a=>a.src==="")&&(s.bg_set=n.map(a=>a[1]?{src:a[1].trim()}:{})),s.bg_set.length<=0)return null;s.bg_set.length>0&&(s.src=s.bg_set[0].src,s.type==="bg-img-set"&&(s.src=s.bg_set))}return s}_initWithFirstElementWithInfo(e){const t=e.find(s=>s.elementInfo!==null&&(s.elementInfo.src||s.elementInfo.srcset));if(!t){this.logger.logMessage("No LCP candidate found."),this.performanceImages=[];return}this.performanceImages=[{...t.elementInfo,label:"lcp"}]}_fillATFWithoutDuplications(e){e.forEach(({element:t,elementInfo:s})=>{this._isDuplicateImage(t)||!s||this.performanceImages.push({...s,label:"above-the-fold"})})}_isDuplicateImage(e){const t=this._getElementInfo(e);if(t===null)return!1;const s=t.type==="img"||t.type==="img-srcset"||t.type==="video",l=t.type==="bg-img"||t.type==="bg-img-set"||t.type==="picture";return(s||l)&&this.performanceImages.some(r=>r.src===t.src)}getResults(){return this.performanceImages}},C=F,x=class{constructor(e,t){this.config=e,this.logger=t,this.lazyRenderElements=[]}async run(){try{const e=this._getLazyRenderElements();e&&this._processElements(e)}catch(e){this.errorCode="script_error",this.logger.logMessage("Script Error: "+e)}}_getLazyRenderElements(){const e=document.querySelectorAll("[data-rocket-location-hash]"),t=this._getSvgUseTargets();return e.length<=0?[]:Array.from(e).filter(l=>this._skipElement(l)?!1:t.includes(l)?(this.logger.logColoredMessage(`Element skipped because of SVG: ${l.tagName}`,"orange"),!1):!0).map(l=>({element:l,depth:this._getElementDepth(l),distance:this._getElementDistance(l),hash:this._getLocationHash(l)}))}_getElementDepth(e){let t=0,s=e.parentElement;for(;s;)t++,s=s.parentElement;return t}_getElementDistance(e){const t=e.getBoundingClientRect(),s=window.pageYOffset||document.documentElement.scrollTop;return Math.max(0,t.top+s-E.getScreenHeight())}_skipElement(e){const t=this.config.skipStrings||["memex"];return!e||!e.id?!1:t.some(s=>e.id.toLowerCase().includes(s.toLowerCase()))}_shouldSkipElement(e,t){if(!e)return!1;for(let s=0;s<t.length;s++){const[l,r]=t[s],i=e.getAttribute(l);if(i&&new RegExp(r,"i").test(i))return!0}return!1}_checkLcrConflict(e){const t=[],s=window.getComputedStyle(e),r=["marginTop","marginRight","marginBottom","marginLeft"].some(o=>parseFloat(s[o])<0);return(r||s.contentVisibility==="auto"||s.contentVisibility==="hidden")&&t.push({element:e,conflicts:[r&&"negative margin",s.contentVisibility==="auto"&&"content-visibility:auto",s.contentVisibility==="hidden"&&"content-visibility:hidden"].filter(Boolean)}),Array.from(e.children).forEach(o=>{const n=window.getComputedStyle(o),c=["marginTop","marginRight","marginBottom","marginLeft"].some(g=>parseFloat(n[g])<0);(c||n.position==="absolute"||n.position==="fixed")&&t.push({element:o,conflicts:[c&&"negative margin",n.position==="absolute"&&"position:absolute",n.position==="fixed"&&"position:fixed"].filter(Boolean)})}),t}_processElements(e){e.forEach(({element:t,depth:s,distance:l,hash:r})=>{if(this._shouldSkipElement(t,this.config.exclusions||[])||r==="No hash detected")return;const i=this._checkLcrConflict(t);if(i.length>0){this.logger.logMessage("Skipping element due to conflicts:",i);return}const o=t.parentElement&&this._getElementDistance(t.parentElement)<this.config.lrc_threshold&&l>=this.config.lrc_threshold,n=o?"green":l===0?"red":"";this.logger.logColoredMessage(`${"	".repeat(s)}${t.tagName} (Depth: ${s}, Distance from viewport bottom: ${l}px)`,n),this.logger.logColoredMessage(`${"	".repeat(s)}Location hash: ${r}`,n),this.logger.logColoredMessage(`${"	".repeat(s)}Dimensions Client Height: ${t.clientHeight}`,n),o&&(this.lazyRenderElements.push(r),this.logger.logMessage(`Element pushed with hash: ${r}`))})}_getXPath(e){return e&&e.id!==""?`//*[@id="${e.id}"]`:this._getElementXPath(e)}_getElementXPath(e){if(e===document.body)return"/html/body";const t=this._getElementPosition(e);return`${this._getElementXPath(e.parentNode)}/${e.nodeName.toLowerCase()}[${t}]`}_getElementPosition(e){let t=1,s=e.previousElementSibling;for(;s;)s.nodeName===e.nodeName&&t++,s=s.previousElementSibling;return t}_getLocationHash(e){return e.hasAttribute("data-rocket-location-hash")?e.getAttribute("data-rocket-location-hash"):"No hash detected"}_getSvgUseTargets(){const e=document.querySelectorAll("use"),t=new Set;return e.forEach(s=>{let l=s.parentElement;for(;l&&l!==document.body;)t.add(l),l=l.parentElement}),Array.from(t)}getResults(){return this.lazyRenderElements}},v=x,M=class{constructor(e,t){this.config=e,this.logger=t,this.aboveTheFoldFonts=[];const s=(Array.isArray(this.config.processed_extensions)&&this.config.processed_extensions.length>0?this.config.processed_extensions:["woff","woff2","ttf"]).map(l=>l.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")).join("|");this.FONT_FILE_REGEX=new RegExp(`\\.(${s})(\\?.*)?$`,"i"),this.EXCLUDED_TAG_NAMES=new Set(["BASE","HEAD","LINK","META","STYLE","TITLE","SCRIPT","IMG","VIDEO","AUDIO","EMBED","OBJECT","IFRAME","NOSCRIPT","TEMPLATE","SLOT","CANVAS","SOURCE","TRACK","PARAM","USE","SYMBOL","BR","HR","WBR","APPLET","ACRONYM","BGSOUND","BIG","BLINK","CENTER","FONT","FRAME","FRAMESET","MARQUEE","NOFRAMES","STRIKE","TT","U","XMP"])}isUrlExcludedFromExternalProcessing(e){if(!e)return!1;const t=this.config.external_font_exclusions||[],s=this.config.preload_fonts_exclusions||[];return[...t,...s].some(r=>e.includes(r))}isExcluded(e,t){const s=this.config.preload_fonts_exclusions,l=new Set(s);return!!(l.has(e)||s.some(r=>e.includes(r))||Array.isArray(t)&&t.length>0&&(t.some(r=>l.has(r))||t.some(r=>s.some(i=>r.includes(i)))))}canElementBeStyledWithFontFamily(e){return!this.EXCLUDED_TAG_NAMES.has(e.tagName)}isElementVisible(e){return E.isElementVisible(e)}cleanUrl(e){try{return e=e.split("?")[0].split("#")[0],new URL(e,window.location.href).href}catch{return e}}async externalStylesheetsDoc(){function e(o){const n={};function a(h){if(!h)return null;const g=h.match(/url\s*\(\s*(['"]?)(.+?)\1\s*\)/);return g?g[2]:null}function c(h){return h?h.replace(/^['"]+|['"]+$/g,"").trim():""}return!o||!Array.isArray(o)?(console.warn("generateFontPairsFromStyleSheets: Input is not a valid array. Received:",o),n):(o.length===0||o.forEach(h=>{if(h&&h.cssRules)try{for(const g of h.cssRules)if(g.type===CSSRule.FONT_FACE_RULE){const u=g,m=c(u.style.getPropertyValue("font-family")),d=u.style.getPropertyValue("font-weight")||"normal",f=u.style.getPropertyValue("font-style")||"normal",p=u.style.getPropertyValue("src"),y=a(p);if(m&&y){const S={family:m,weight:d,style:f};n[y]||(n[y]=[]),n[y].some(w=>w.family===S.family&&w.weight===S.weight&&w.style===S.style)||n[y].push(S)}}}catch(g){console.warn("Error processing CSS rules from a stylesheet:",g,h)}else h&&!h.cssRules&&console.warn("Skipping a stylesheet as its cssRules are not accessible or it is empty:",h)}),n)}const t=[...document.querySelectorAll('link[rel="stylesheet"]')].filter(o=>{try{const n=new URL(o.href),a=new URL(window.location.href);return n.origin===a.origin?!1:!this.isUrlExcludedFromExternalProcessing(o.href)}catch{return!1}});if(t.length===0)return this.logger.logMessage("No external CSS links found to process."),{styleSheets:[],fontPairs:{}};const s=t.map(o=>fetch(o.href,{mode:"cors"}).then(n=>n.ok?n.text():(console.warn(`Failed to fetch external CSS from ${o.href}: ${n.status} ${n.statusText}`),null)).catch(n=>(console.error(`Network error fetching external CSS from ${o.href}:`,n),null))),l=await Promise.all(s),r=[];l.forEach(o=>{if(o&&o.trim()!=="")try{const n=new CSSStyleSheet;n.replaceSync(o),r.push(n)}catch(n){console.error("Could not parse fetched CSS into a stylesheet:",n,`
CSS (first 200 chars): ${o.substring(0,200)}...`)}}),r.length>0?this.logger.logMessage(`[Beacon] ${r.length} stylesheet(s) fetched and parsed into CSSStyleSheet objects.`):this.logger.logMessage("[Beacon] No stylesheets were successfully parsed from the fetched CSS.");const i=e(r);return{styleSheets:r,fontPairs:i}}async _initializeExternalFontSheets(){this.logger.logMessage("Initializing external font stylesheets...");try{const e=await this.externalStylesheetsDoc();this.externalParsedSheets=e.styleSheets||[],this.externalParsedPairs=e.fontPairs||[],this.logger.logMessage(`Successfully parsed ${this.externalParsedSheets.length} external font stylesheets.`)}catch(e){this.logger.logMessage("Error initializing external font stylesheets:",e),this.externalParsedSheets=[]}}getNetworkLoadedFonts(){return new Map(window.performance.getEntriesByType("resource").filter(e=>this.FONT_FILE_REGEX.test(e.name)).map(e=>[this.cleanUrl(e.name),e.name]))}async getFontFaceRules(){const e={},t=new Set,s=(n,a=null)=>{const c=n.style.getPropertyValue("src"),h=n.style.getPropertyValue("font-family").replace(/['"]/g,"").trim(),g=n.style.getPropertyValue("font-weight")||"400",u=n.style.getPropertyValue("font-style")||"normal";e[h]||(e[h]={urls:[],variations:new Set});const d=(f=>{if(!f)return null;const p=f.match(/url\s*\(\s*(['"]?)(.+?)\1\s*\)/);return p?p[2]:null})(c);if(d){let f=d;a&&(f=new URL(f,a).href);const p=this.cleanUrl(f);e[h].urls.includes(p)||(e[h].urls.push(p),e[h].variations.add(JSON.stringify({weight:g,style:u})))}},l=async n=>{try{const a=n.href;if(this.isUrlExcludedFromExternalProcessing(a)||t.has(a))return;t.add(a);const c=await fetch(a,{mode:"cors"});if(!c.ok){this.logger.logMessage(`Failed to fetch @import CSS: ${c.status}`);return}const h=await c.text(),g=new CSSStyleSheet;g.replaceSync(h),Array.from(g.cssRules||[]).forEach(u=>{u instanceof CSSFontFaceRule&&s(u,a)})}catch(a){this.logger.logMessage(`Error processing @import rule: ${a.message}`)}},r=async n=>{try{const a=Array.from(n.cssRules||[]);for(const c of a)c instanceof CSSFontFaceRule?s(c,n.href):c instanceof CSSImportRule?c.styleSheet?await r(c.styleSheet):await l(c):c.styleSheet&&await r(c.styleSheet)}catch(a){if(a.name==="SecurityError"&&n.href){if(this.isUrlExcludedFromExternalProcessing(n.href)||t.has(n.href))return;t.add(n.href);try{const c=await fetch(n.href,{mode:"cors"});if(c.ok){const h=await c.text(),g=new CSSStyleSheet;g.replaceSync(h),Array.from(g.cssRules||[]).forEach(d=>{d instanceof CSSFontFaceRule&&s(d,n.href)});const u=/@import\s+url\(['"]?([^'")]+)['"]?\);?/g;let m;for(;(m=u.exec(h))!==null;){const d=new URL(m[1],n.href).href;if(!this.isUrlExcludedFromExternalProcessing(d)&&!t.has(d)){t.add(d);try{const f=await fetch(d,{mode:"cors"});if(f.ok){const p=await f.text(),y=new CSSStyleSheet;y.replaceSync(p),Array.from(y.cssRules||[]).forEach(S=>{S instanceof CSSFontFaceRule&&s(S,d)})}}catch(f){this.logger.logMessage(`Error fetching @import ${d}: ${f.message}`)}}}}}catch(c){this.logger.logMessage(`Error fetching stylesheet ${n.href}: ${c.message}`)}}else this.logger.logMessage(`Error processing stylesheet: ${a.message}`)}},i=Array.from(document.styleSheets);for(const n of i)await r(n);const o=document.querySelectorAll("style");for(const n of o){const a=n.textContent||n.innerHTML||"",c=/@import\s+url\s*\(\s*['"]?([^'")]+)['"]?\s*\)\s*;?/g;let h;for(;(h=c.exec(a))!==null;){const g=h[1];if(!this.isUrlExcludedFromExternalProcessing(g)&&!t.has(g)){t.add(g);try{const u=await fetch(g,{mode:"cors"});if(u.ok){const m=await u.text(),d=new CSSStyleSheet;d.replaceSync(m),Array.from(d.cssRules||[]).forEach(f=>{f instanceof CSSFontFaceRule&&s(f,g)})}}catch(u){this.logger.logMessage(`Error fetching inline @import ${g}: ${u.message}`)}}}}return Object.values(e).forEach(n=>{n.variations=Array.from(n.variations).map(a=>JSON.parse(a))}),e}isElementAboveFold(e){if(!this.isElementVisible(e))return!1;const t=e.getBoundingClientRect(),s=window.pageYOffset||document.documentElement.scrollTop,l=t.top+s,r=window.innerHeight||document.documentElement.clientHeight;return l<=r}canElementBeProcessed(e){return this.canElementBeStyledWithFontFamily(e)&&this.isElementAboveFold(e)}async run(){await document.fonts.ready,await this._initializeExternalFontSheets();const e=this.getNetworkLoadedFonts(),t=await this.getFontFaceRules(),s=new Map,l=await this.processExternalFonts(this.externalParsedPairs);Array.from(document.getElementsByTagName("*")).filter(o=>this.canElementBeProcessed(o)).forEach(o=>{const n=(a,c=null)=>{if(!a||!this.isElementVisible(o))return;const h=a.fontFamily.split(",")[0].replace(/['"]+/g,"").trim();if((c?a.content!=="none"&&a.content!=='""':o.textContent.trim())&&t[h]){let u=t[h].urls;!this.isExcluded(h,u)&&!s.has(h)&&(s.set(h,{elements:new Set,urls:u,variations:t[h].variations}),s.get(h).elements.add(o))}};try{n(window.getComputedStyle(o)),["::before","::after"].forEach(a=>{n(window.getComputedStyle(o,a),a)})}catch(a){this.logger.logMessage("Error processing element:",a)}});const i=this.summarizeMatches(l,s,e);if(!Object.keys(i.allFonts).length&&!Object.keys(i.externalFonts).length&&!Object.keys(i.hostedFonts).length){this.logger.logMessage("No fonts found above the fold.");return}this.logger.logMessage("Above the fold fonts:",i),this.aboveTheFoldFonts=[...new Set(Object.values(i.allFonts).flatMap(o=>o.variations.map(n=>n.url)))]}summarizeMatches(e,t,s){const l={},r={};return t.size>0&&t.forEach((i,o)=>{if(i.variations){const n=Array.from(i.elements),a=n.filter(h=>this.isElementAboveFold(h)),c=n.filter(h=>!this.isElementAboveFold(h));i.variations.forEach(h=>{let g=null;for(const u of i.urls){const m=this.cleanUrl(u);if(s.has(m)){g=s.get(m);break}}g&&(l[o]||(l[o]={type:"hosted",variations:[],elementCount:{aboveFold:a.length,belowFold:c.length,total:n.length},urlCount:{aboveFold:new Set,belowFold:new Set}}),l[o].variations.push({weight:h.weight,style:h.style,url:g,elementCount:{aboveFold:a.length,belowFold:c.length,total:n.length}}),a.length>0&&l[o].urlCount.aboveFold.add(g),c.length>0&&l[o].urlCount.belowFold.add(g))}),l[o]&&(r[o]={variations:l[o].variations,elementCount:{...l[o].elementCount},urlCount:{...l[o].urlCount}})}}),Object.keys(e).length>0&&Object.entries(e).forEach(([i,o])=>{const n=Array.from(o.elements).filter(c=>this.isElementAboveFold(c)),a=Array.from(o.elements).filter(c=>!this.isElementAboveFold(c));(o.elementCount.aboveFold>0||n.length>0)&&o.variations.forEach(c=>{l[c.family]||(l[c.family]={type:"external",variations:[],elementCount:{aboveFold:0,belowFold:0,total:0},urlCount:{aboveFold:new Set,belowFold:new Set}}),l[c.family].variations.push({weight:c.weight,style:c.style,url:i,elementCount:{aboveFold:n.length,belowFold:a.length,total:o.elements.length}}),l[c.family].elementCount.aboveFold+=n.length,l[c.family].elementCount.belowFold+=a.length,l[c.family].elementCount.total+=o.elements.length,n.length>0&&l[c.family].urlCount.aboveFold.add(i),a.length>0&&l[c.family].urlCount.belowFold.add(i)})}),Object.values(l).forEach(i=>{i.urlCount={aboveFold:i.urlCount.aboveFold.size,belowFold:i.urlCount.belowFold.size,total:new Set([...i.urlCount.aboveFold,...i.urlCount.belowFold]).size}}),Object.values(r).forEach(i=>{i.urlCount.aboveFold instanceof Set&&(i.urlCount={aboveFold:i.urlCount.aboveFold.size,belowFold:i.urlCount.belowFold.size,total:new Set([...i.urlCount.aboveFold,...i.urlCount.belowFold]).size})}),{externalFonts:Object.fromEntries(Object.entries(e).filter(i=>i[1].elementCount.aboveFold>0)),hostedFonts:r,allFonts:l}}async processExternalFonts(e){const t=new Map,s=Array.from(document.getElementsByTagName("*")).filter(i=>this.canElementBeProcessed(i)),l=new Map;Object.entries(e).forEach(([i,o])=>{o.forEach(n=>{const a=`${n.family}|${n.weight}|${n.style}`;l.set(a,{url:i,...n})})});const r=i=>{const o=i.fontFamily.split(",")[0].replace(/['"]+/g,"").trim(),n=i.fontWeight,a=i.fontStyle,c=`${o}|${n}|${a}`;let h=l.get(c);if(!h&&n!=="400"){const g=`${o}|400|${a}`;h=l.get(g)}return h};return s.forEach(i=>{if(i.textContent.trim()){const o=window.getComputedStyle(i),n=r(o);n&&!this.isExcluded(n.family,[n.url])&&!t.has(n.url)&&(t.set(n.url,{elements:new Set,variations:new Set}),t.get(n.url).elements.add(i),t.get(n.url).variations.add(JSON.stringify({family:n.family,weight:n.weight,style:n.style})))}["::before","::after"].forEach(o=>{const n=window.getComputedStyle(i,o);if(n.content!=="none"&&n.content!=='""'){const a=r(n);a&&!this.isExcluded(a.family,[a.url])&&!t.has(a.url)&&(t.set(a.url,{elements:new Set,variations:new Set}),t.get(a.url).elements.add(i),t.get(a.url).variations.add(JSON.stringify({family:a.family,weight:a.weight,style:a.style})))}})}),Object.fromEntries(Array.from(t.entries()).map(([i,o])=>[i,{elementCount:{aboveFold:Array.from(o.elements).filter(n=>this.isElementAboveFold(n)).length,total:o.elements.size},variations:Array.from(o.variations).map(n=>JSON.parse(n)),elements:Array.from(o.elements)}]))}getResults(){return this.aboveTheFoldFonts}},B=M,A=class{constructor(e,t){this.logger=t,this.result=[],this.excludedPatterns=e.preconnect_external_domain_exclusions,this.eligibleElements=e.preconnect_external_domain_elements,this.matchedItems=new Set,this.excludedItems=new Set}async run(){document.querySelectorAll(`${this.eligibleElements.join(", ")}[src], ${this.eligibleElements.join(", ")}[href], ${this.eligibleElements.join(", ")}[rel], ${this.eligibleElements.join(", ")}[type]`).forEach(t=>this.processElement(t)),this.logger.logMessage({matchedItems:this.getMatchedItems(),excludedItems:Array.from(this.excludedItems)})}processElement(e){try{const t=new URL(e.src||e.href||"",location.href);if(this.isExcluded(e)){this.excludedItems.add(this.createExclusionObject(t,e));return}this.isExternalDomain(t)&&(this.matchedItems.add(`${t.hostname}-${e.tagName.toLowerCase()}`),this.result=[...new Set(this.result.concat(t.origin))])}catch(t){this.logger.logMessage(t)}}isExcluded(e){const t=e.outerHTML.substring(0,e.outerHTML.indexOf(">")+1);return this.excludedPatterns.some(s=>t.includes(s))}isExcludedByDomain(e){return this.excludedPatterns.some(t=>t.type==="domain"&&e.hostname.includes(t.value))}isExternalDomain(e){return e.hostname!==location.hostname&&e.hostname}createExclusionObject(e,t){return{domain:e.hostname,elementType:t.tagName.toLowerCase()}}getMatchedItems(){return Array.from(this.matchedItems).map(e=>{const t=e.lastIndexOf("-");return[e.substring(0,t),e.substring(t+1)]})}getResults(){return this.result}},P=A,R=class{constructor(e){this.enabled=e}logMessage(e,t=""){if(this.enabled){if(t!==""){console.log(e,t);return}console.log(e)}}logColoredMessage(e,t="green"){this.enabled&&console.log(`%c${e}`,`color: ${t};`)}},I=R,T=class{constructor(e){this.config=e,this.lcpBeacon=null,this.lrcBeacon=null,this.preloadFontsBeacon=null,this.preconnectExternalDomainBeacon=null,this.infiniteLoopId=null,this.errorCode="",this.logger=new I(this.config.debug)}async init(){if(this.scriptTimer=new Date,!await this._isValidPreconditions()){this._finalize();return}if(E.isPageScrolled()){this.logger.logMessage("Bailing out because the page has been scrolled"),this._finalize();return}this.infiniteLoopId=setTimeout(()=>{this._handleInfiniteLoop()},1e4);const e=await this._getGeneratedBefore(),t=this.config.status.atf&&(e===!1||e.lcp===!1),s=this.config.status.lrc&&(e===!1||e.lrc===!1),l=this.config.status.preload_fonts&&(e===!1||e.preload_fonts===!1),r=this.config.status.preconnect_external_domain&&(e===!1||e.preconnect_external_domain===!1);t?(this.lcpBeacon=new C(this.config,this.logger),await this.lcpBeacon.run()):this.logger.logMessage("Not running BeaconLcp because data is already available or feature is disabled"),s?(this.lrcBeacon=new v(this.config,this.logger),await this.lrcBeacon.run()):this.logger.logMessage("Not running BeaconLrc because data is already available or feature is disabled"),l?(this.preloadFontsBeacon=new B(this.config,this.logger),await this.preloadFontsBeacon.run()):this.logger.logMessage("Not running BeaconPreloadFonts because data is already available or feature is disabled"),r?(this.preconnectExternalDomainBeacon=new P(this.config,this.logger),await this.preconnectExternalDomainBeacon.run()):this.logger.logMessage("Not running BeaconPreconnectExternalDomain because data is already available or feature is disabled"),t||s||l||r?this._saveFinalResultIntoDB():(this.logger.logMessage("Not saving results into DB as no beacon features ran."),this._finalize())}async _isValidPreconditions(){const e={width:this.config.width_threshold,height:this.config.height_threshold};return E.isNotValidScreensize(this.config.is_mobile,e)?(this.logger.logMessage("Bailing out because screen size is not acceptable"),!1):!0}async _getGeneratedBefore(){if(!E.isPageCached())return!1;let e=new FormData;return e.append("action","rocket_check_beacon"),e.append("rocket_beacon_nonce",this.config.nonce),e.append("url",this.config.url),e.append("is_mobile",this.config.is_mobile),(await fetch(this.config.ajax_url,{method:"POST",credentials:"same-origin",body:e}).then(s=>s.json())).data}_saveFinalResultIntoDB(){const e={lcp:this.lcpBeacon?this.lcpBeacon.getResults():null,lrc:this.lrcBeacon?this.lrcBeacon.getResults():null,preload_fonts:this.preloadFontsBeacon?this.preloadFontsBeacon.getResults():null,preconnect_external_domain:this.preconnectExternalDomainBeacon?this.preconnectExternalDomainBeacon.getResults():null},t=new FormData;t.append("action","rocket_beacon"),t.append("rocket_beacon_nonce",this.config.nonce),t.append("url",this.config.url),t.append("is_mobile",this.config.is_mobile),t.append("status",this._getFinalStatus()),t.append("results",JSON.stringify(e)),fetch(this.config.ajax_url,{method:"POST",credentials:"same-origin",body:t,headers:{"wpr-saas-no-intercept":!0}}).then(s=>s.json()).then(s=>{this.logger.logMessage(s.data.lcp)}).catch(s=>{this.logger.logMessage(s)}).finally(()=>{this._finalize()})}_getFinalStatus(){return this.errorCode!==""?this.errorCode:10<=(new Date-this.scriptTimer)/1e3?"timeout":"success"}_handleInfiniteLoop(){this._saveFinalResultIntoDB()}_finalize(){document.querySelector('[data-name="wpr-wpr-beacon"]').setAttribute("beacon-completed","true"),clearTimeout(this.infiniteLoopId)}},b=T;(e=>{if(!e)return;const t=new b(e);if(document.readyState!=="loading"){setTimeout(()=>{t.init()},e.delay);return}document.addEventListener("DOMContentLoaded",()=>{setTimeout(()=>{t.init()},e.delay)})})(window.rocket_beacon_data);var L=b})();
//# sourceMappingURL=wpr-beacon.min.js.map
