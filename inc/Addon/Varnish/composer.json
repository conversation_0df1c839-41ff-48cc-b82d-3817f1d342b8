{"name": "wp-media/module-varnish", "description": "Varnish <PERSON>don for WP Rocket", "homepage": "https://github.com/wp-media/module-varnish", "license": "GPL-2.0+", "authors": [{"name": "WP Media", "email": "<EMAIL>", "homepage": "https://wp-media.me"}], "type": "library", "config": {"sort-packages": true}, "support": {"issues": "https://github.com/wp-media/module-varnish/issues", "source": "https://github.com/wp-media/module-varnish"}, "require-dev": {"php": "^7 || ^8", "brain/monkey": "^2.0", "coenjacobs/mozart": "^0.7", "dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "league/container": "^3.3", "phpcompatibility/phpcompatibility-wp": "^2.0", "phpstan/phpstan": "^0.12.3", "phpunit/phpunit": "^7", "psr/container": "1.0.0", "roave/security-advisories": "dev-master", "szepeviktor/phpstan-wordpress": "^0.7", "wp-coding-standards/wpcs": "^2", "wp-media/event-manager": "^3.1", "wp-media/options": "^3.0", "wp-media/phpunit": "1.1.6"}, "autoload": {"psr-4": {"WP_Rocket\\Addon\\Varnish\\": "."}}, "autoload-dev": {"psr-4": {"WP_Rocket\\Tests\\": "Tests/", "WP_Rocket\\Dependencies\\": "Dependencies/"}}, "extra": {"mozart": {"dep_namespace": "WP_Rocket\\Dependencies\\", "dep_directory": "/Dependencies/", "classmap_directory": "/classes/dependencies/", "classmap_prefix": "WP_Rocket_", "packages": ["league/container"]}}, "scripts": {"test-unit": "\"vendor/bin/wpmedia-phpunit\" unit path=Tests/Unit", "test-integration": "\"vendor/bin/wpmedia-phpunit\" integration path=Tests/Integration/", "run-tests": ["@test-unit", "@test-integration"], "install-codestandards": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin::run", "phpcs": "\"vendor/bin/phpcs\" .", "phpcs:fix": "\"vendor/bin/phpcbf\" ", "phpstan": "\"vendor/bin/phpstan\" analyse", "post-install-cmd": ["\"vendor/bin/mozart\" compose", "composer dump-autoload"], "post-update-cmd": ["\"vendor/bin/mozart\" compose", "composer dump-autoload"]}}